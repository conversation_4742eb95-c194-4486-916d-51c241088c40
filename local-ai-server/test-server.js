// Simple test server to check if basic setup works
console.log('Starting test server...');

try {
    const express = require('express');
    console.log('✅ Express loaded');
    
    const app = express();
    const port = 9876;
    
    app.get('/', (req, res) => {
        res.json({ message: 'Test server is working!' });
    });
    
    app.listen(port, () => {
        console.log(`✅ Test server running on http://localhost:${port}`);
    });
    
} catch (error) {
    console.error('❌ Error starting test server:', error);
    process.exit(1);
}
