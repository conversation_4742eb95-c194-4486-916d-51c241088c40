<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRA Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.6;
            margin-top: 4px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a6fd8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #ccc;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .status-bar {
            padding: 8px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e1e5e9;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .status-dot.disconnected {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🎭 Harmonic Resonance Agent</h1>
            <p>Chat with your AI companion powered by LM Studio</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    Hello! I'm your Harmonic Resonance Agent. I'm connected to LM Studio and can help you with various tasks. How can I assist you today?
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" class="input-field" placeholder="Type your message..." maxlength="1000">
            <button id="sendButton" class="send-button">Send</button>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Connected to HRA Server</span>
            </div>
            <div id="modelInfo">Model: Gemma 3-4B</div>
        </div>
    </div>

    <script>
        class HRAChat {
            constructor() {
                this.serverUrl = 'http://localhost:9876';
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatMessages = document.getElementById('chatMessages');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                
                this.setupEventListeners();
                this.checkServerStatus();
                this.setWelcomeTime();
            }

            setupEventListeners() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            setWelcomeTime() {
                document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();
            }

            async checkServerStatus() {
                try {
                    const response = await fetch(`${this.serverUrl}/api/llm/status`);
                    if (response.ok) {
                        const data = await response.json();
                        this.updateStatus(true, `Connected - Active: ${data.activeProvider}`);
                    } else {
                        this.updateStatus(false, 'Server error');
                    }
                } catch (error) {
                    this.updateStatus(false, 'Disconnected from HRA Server');
                }
            }

            updateStatus(connected, message) {
                this.statusDot.className = `status-dot ${connected ? '' : 'disconnected'}`;
                this.statusText.textContent = message;
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                this.addMessage('user', message);
                this.messageInput.value = '';
                this.sendButton.disabled = true;
                this.showTypingIndicator();

                try {
                    const response = await fetch(`${this.serverUrl}/api/chat`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            context: {
                                source: 'web_chat',
                                timestamp: new Date().toISOString(),
                                interface: 'hra_chat'
                            }
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.hideTypingIndicator();

                        // Handle different response formats
                        let responseText;
                        if (typeof data.response === 'string') {
                            responseText = data.response;
                        } else if (typeof data.response === 'object') {
                            responseText = JSON.stringify(data.response, null, 2);
                        } else if (data.choices && data.choices[0] && data.choices[0].message) {
                            responseText = data.choices[0].message.content;
                        } else {
                            responseText = JSON.stringify(data, null, 2);
                        }

                        this.addMessage('assistant', responseText);
                    } else {
                        throw new Error('Server error');
                    }
                } catch (error) {
                    this.hideTypingIndicator();
                    this.addMessage('assistant', 'Sorry, I encountered an error. Please check if the HRA server is running on port 9876.');
                    console.error('Chat error:', error);
                }

                this.sendButton.disabled = false;
                this.messageInput.focus();
            }

            addMessage(sender, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.innerHTML = `
                    ${content}
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                `;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            showTypingIndicator() {
                this.typingIndicator.style.display = 'block';
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            hideTypingIndicator() {
                this.typingIndicator.style.display = 'none';
            }
        }

        // Initialize chat when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new HRAChat();
        });
    </script>
</body>
</html>
