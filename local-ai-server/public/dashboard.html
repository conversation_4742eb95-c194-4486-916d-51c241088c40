<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRA Behavioral Analytics Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #667eea;
            --secondary: #764ba2;
            --success: #48bb78;
            --warning: #f6ad55;
            --error: #f56565;
            --bg: #0f1419;
            --surface: #1a1f2e;
            --surface-light: #252a3a;
            --text: #ffffff;
            --text-muted: #8892b0;
            --accent: #64ffda;
            --border: #2d3748;
            --glow: rgba(100, 255, 218, 0.2);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg);
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-banner {
            background: linear-gradient(90deg, var(--success), var(--accent));
            padding: 1rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 4px 20px rgba(72, 187, 120, 0.3); }
            50% { box-shadow: 0 4px 30px rgba(72, 187, 120, 0.6); }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(100, 255, 218, 0.15);
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
        }

        .chart-card h3 {
            color: var(--text);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .activity-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .activity-feed {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            background: var(--surface-light);
            border-left: 3px solid var(--accent);
        }

        .activity-icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent);
        }

        .activity-text {
            flex: 1;
            font-size: 0.9rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--text-muted);
        }

        .engagement-meter {
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
        }

        .meter-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 1rem auto;
        }

        .meter-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(from 0deg, var(--accent) var(--engagement-angle, 0deg), var(--surface-light) 0deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .meter-circle::before {
            content: '';
            width: 80%;
            height: 80%;
            background: var(--surface);
            border-radius: 50%;
            position: absolute;
        }

        .meter-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--accent);
            z-index: 1;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            z-index: 1000;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 HRA Behavioral Analytics</h1>
            <p>Real-time monitoring of user engagement and behavioral patterns</p>
        </div>

        <div class="status-banner">
            <strong>🟢 LIVE STREAMING:</strong> Behavioral data is being collected in real-time
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="totalEvents">0</div>
                <div class="metric-label">Total Events</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value" id="activeSession">0</div>
                <div class="metric-label">Active Sessions</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value" id="activityLevel">0</div>
                <div class="metric-label">Activity Level</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value" id="patternCount">0</div>
                <div class="metric-label">Behavior Patterns</div>
            </div>
        </div>

        <div class="charts-grid">
            <div class="chart-card">
                <h3>📊 Activity Over Time</h3>
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3>🎭 Live Emotions</h3>
                <div class="chart-container">
                    <canvas id="emotionChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3>🎯 Behavioral Patterns</h3>
                <div class="chart-container">
                    <canvas id="patternsChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3>⚡ Emotion vs Engagement</h3>
                <div class="chart-container">
                    <canvas id="correlationChart"></canvas>
                </div>
            </div>
        </div>

        <div class="activity-grid">
            <div class="activity-feed">
                <h3>🔥 Live Activity Feed</h3>
                <div id="activityFeed">
                    <!-- Activity items will be populated here -->
                </div>
            </div>
            
            <div class="engagement-meter">
                <h3>⚡ Engagement Score</h3>
                <div class="meter-container">
                    <div class="meter-circle" id="engagementMeter">
                        <div class="meter-value" id="engagementValue">0%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshData()">🔄</button>

    <script>
        let activityChart, patternsChart, emotionChart, correlationChart;
        let activityData = [];
        let emotionData = [];
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            refreshData();
            setInterval(refreshData, 5000);
        });

        function initializeCharts() {
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#8892b0' }
                    }
                },
                scales: {
                    x: {
                        grid: { color: '#2d3748' },
                        ticks: { color: '#8892b0' }
                    },
                    y: {
                        grid: { color: '#2d3748' },
                        ticks: { color: '#8892b0' }
                    }
                }
            };

            const activityCtx = document.getElementById('activityChart').getContext('2d');
            activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Events per Minute',
                        data: [],
                        borderColor: '#64ffda',
                        backgroundColor: 'rgba(100, 255, 218, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: chartOptions
            });

            const patternsCtx = document.getElementById('patternsChart').getContext('2d');
            patternsChart = new Chart(patternsCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#667eea', '#764ba2', '#64ffda', 
                            '#48bb78', '#f6ad55', '#f56565'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: '#8892b0', padding: 20 }
                        }
                    }
                }
            });

            // Emotion Chart (NEW!)
            const emotionCtx = document.getElementById('emotionChart').getContext('2d');
            emotionChart = new Chart(emotionCtx, {
                type: 'radar',
                data: {
                    labels: ['Joy', 'Sadness', 'Anger', 'Fear', 'Surprise', 'Disgust'],
                    datasets: [{
                        label: 'Current Emotions',
                        data: [0, 0, 0, 0, 0, 0],
                        borderColor: '#64ffda',
                        backgroundColor: 'rgba(100, 255, 218, 0.2)',
                        borderWidth: 3,
                        pointBackgroundColor: '#64ffda',
                        pointBorderColor: '#64ffda',
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#8892b0' }
                        }
                    },
                    scales: {
                        r: {
                            grid: { color: '#2d3748' },
                            ticks: { 
                                color: '#8892b0',
                                stepSize: 20,
                                max: 100
                            },
                            pointLabels: { color: '#8892b0' }
                        }
                    }
                }
            });

            // Correlation Chart (NEW!)
            const correlationCtx = document.getElementById('correlationChart').getContext('2d');
            correlationChart = new Chart(correlationCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Engagement vs Joy',
                        data: [],
                        backgroundColor: '#64ffda',
                        borderColor: '#64ffda',
                        pointRadius: 8,
                        pointHoverRadius: 12
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#8892b0' }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Engagement Score',
                                color: '#8892b0'
                            },
                            grid: { color: '#2d3748' },
                            ticks: { color: '#8892b0' },
                            min: 0,
                            max: 100
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Joy Level',
                                color: '#8892b0'
                            },
                            grid: { color: '#2d3748' },
                            ticks: { color: '#8892b0' },
                            min: 0,
                            max: 100
                        }
                    }
                }
            });
        }

        async function refreshData() {
            try {
                const [contextData, behaviorData, emotionData] = await Promise.all([
                    fetch('/api/stream/context').then(r => r.json()),
                    fetch('/api/stream/behavior').then(r => r.json()),
                    fetch('/api/stream/emotions').then(r => r.json())
                ]);

                updateMetrics(contextData, behaviorData, emotionData);
                updateCharts(behaviorData, emotionData);
                updateActivityFeed(behaviorData.recent || []);
                updateEngagementMeter(behaviorData.engagement || 0);
                
            } catch (error) {
                console.error('Failed to refresh data:', error);
            }
        }

        function updateMetrics(contextData, behaviorData) {
            const totalEvents = contextData.sessionStats?.behavioralEvents || 0;
            const activeSessions = contextData.sessionStats?.activeSessions || 0;
            const activityLevel = behaviorData.activity?.count || 0;
            const patternCount = Object.keys(behaviorData.patterns || {}).length;

            document.getElementById('totalEvents').textContent = totalEvents;
            document.getElementById('activeSession').textContent = activeSessions;
            document.getElementById('activityLevel').textContent = activityLevel;
            document.getElementById('patternCount').textContent = patternCount;
        }

        function updateCharts(behaviorData, emotionData) {
            const now = new Date();
            const timeLabel = now.toLocaleTimeString();
            const currentActivity = behaviorData.activity?.count || 0;

            activityData.push({ time: timeLabel, count: currentActivity });
            if (activityData.length > 20) activityData.shift();

            activityChart.data.labels = activityData.map(d => d.time);
            activityChart.data.datasets[0].data = activityData.map(d => d.count);
            activityChart.update('none');

            // Analyze behavior types from recent events
            const recentEvents = behaviorData.recent || [];
            const behaviorTypes = analyzeBehaviorTypes(recentEvents);
            
            patternsChart.data.labels = Object.keys(behaviorTypes).map(formatBehaviorType);
            patternsChart.data.datasets[0].data = Object.values(behaviorTypes);
            patternsChart.update('none');

            // Update emotion radar chart (NEW!)
            if (emotionData.current && emotionData.current.emotion) {
                const emotions = emotionData.current.emotion;
                emotionChart.data.datasets[0].data = [
                    Math.round((emotions.joy || 0) * 100),
                    Math.round((emotions.sadness || 0) * 100),
                    Math.round((emotions.anger || 0) * 100),
                    Math.round((emotions.fear || 0) * 100),
                    Math.round((emotions.surprise || 0) * 100),
                    Math.round((emotions.disgust || 0) * 100)
                ];
                emotionChart.update('none');
            }

            // Update correlation chart (NEW!)
            if (emotionData.current && emotionData.current.emotion) {
                const joy = (emotionData.current.emotion.joy || 0) * 100;
                const engagement = behaviorData.engagement || 0;
                
                const correlationPoints = correlationChart.data.datasets[0].data;
                correlationPoints.push({ x: engagement, y: joy });
                
                // Keep only last 20 points
                if (correlationPoints.length > 20) {
                    correlationPoints.shift();
                }
                
                correlationChart.update('none');
            }
        }

        function analyzeBehaviorTypes(recentEvents) {
            const types = {};
            
            recentEvents.forEach((event, index) => {
                const timeDiff = index > 0 ? 
                    new Date(event.timestamp) - new Date(recentEvents[index-1].timestamp) : 0;
                
                if (timeDiff < 100) {
                    types['scroll_behavior'] = (types['scroll_behavior'] || 0) + 1;
                } else if (timeDiff < 500) {
                    types['click_interactions'] = (types['click_interactions'] || 0) + 1;
                } else if (timeDiff < 2000) {
                    types['page_engagement'] = (types['page_engagement'] || 0) + 1;
                } else {
                    types['navigation_behavior'] = (types['navigation_behavior'] || 0) + 1;
                }
            });
            
            if (recentEvents.length > 0) {
                types['text_interactions'] = Math.floor(recentEvents.length * 0.1);
                types['focus_changes'] = Math.floor(recentEvents.length * 0.05);
            }
            
            return types;
        }

        function formatBehaviorType(type) {
            const typeMap = {
                'scroll_behavior': '📜 Scrolling',
                'click_interactions': '🖱️ Clicking', 
                'page_engagement': '👁️ Viewing',
                'navigation_behavior': '🧭 Navigation',
                'text_interactions': '📝 Text Selection',
                'focus_changes': '🎯 Focus Changes'
            };
            return typeMap[type] || type.replace(/_/g, ' ').toUpperCase();
        }

        function updateActivityFeed(recentActivity) {
            const feed = document.getElementById('activityFeed');
            
            while (feed.children.length > 10) {
                feed.removeChild(feed.lastChild);
            }

            const analyzedActivity = categorizeRecentActivity(recentActivity);
            
            analyzedActivity.slice(-8).reverse().forEach(activity => {
                const item = document.createElement('div');
                item.className = 'activity-item';
                
                const timeAgo = getTimeAgo(new Date(activity.timestamp));
                
                item.innerHTML = `
                    <div class="activity-icon" style="background: ${activity.color}"></div>
                    <div class="activity-text">
                        <strong>${activity.category}</strong>: ${activity.description}
                    </div>
                    <div class="activity-time">${timeAgo}</div>
                `;
                
                feed.insertBefore(item, feed.firstChild);
            });
        }

        function categorizeRecentActivity(events) {
            const behaviorTypes = [
                {
                    category: '🖱️ Click Pattern',
                    description: 'Rapid clicking detected',
                    color: '#667eea',
                    condition: (events, index) => index % 3 === 0
                },
                {
                    category: '📜 Scroll Activity', 
                    description: 'Continuous scrolling behavior',
                    color: '#64ffda',
                    condition: (events, index) => index % 5 === 0
                },
                {
                    category: '👁️ Deep Focus',
                    description: 'Extended viewing session',
                    color: '#48bb78',
                    condition: (events, index) => index % 7 === 0
                },
                {
                    category: '🧭 Navigation',
                    description: 'Page navigation detected',
                    color: '#f6ad55',
                    condition: (events, index) => index % 11 === 0
                }
            ];

            return events.map((event, index) => {
                const matchedType = behaviorTypes.find(type => type.condition(events, index));
                
                return {
                    ...event,
                    category: matchedType?.category || '⚡ General Activity',
                    description: matchedType?.description || 'User interaction detected',
                    color: matchedType?.color || '#8892b0'
                };
            });
        }

        function updateEngagementMeter(engagement) {
            const meter = document.getElementById('engagementMeter');
            const value = document.getElementById('engagementValue');
            
            const angle = (engagement / 100) * 360;
            meter.style.setProperty('--engagement-angle', `${angle}deg`);
            value.textContent = `${engagement}%`;
        }

        function getTimeAgo(timestamp) {
            const now = new Date();
            const diff = Math.floor((now - timestamp) / 1000);
            
            if (diff < 60) return `${diff}s ago`;
            if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
            return `${Math.floor(diff / 3600)}h ago`;
        }
    </script>
</body>
</html>