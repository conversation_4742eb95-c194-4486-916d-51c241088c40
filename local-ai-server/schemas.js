// JSO<PERSON> Schemas for LM Studio API responses
// Ensures consistent, structured outputs from Gemma model
// Format: LM Studio structured output format

const schemas = {
    // Schema for emotion analysis responses
    emotionAnalysis: {
        type: "json_schema",
        json_schema: {
            name: "emotion_analysis",
            strict: true,
            schema: {
                type: "object",
                properties: {
                    primary_emotion: {
                        type: "string",
                        enum: ["joy", "sadness", "anger", "fear", "surprise", "disgust", "neutral", "excitement", "anxiety", "contentment"]
                    },
                    confidence: {
                        type: "number",
                        minimum: 0,
                        maximum: 1
                    },
                    emotional_intensity: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    secondary_emotions: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                emotion: {
                                    type: "string",
                                    enum: ["joy", "sadness", "anger", "fear", "surprise", "disgust", "neutral", "excitement", "anxiety", "contentment"]
                                },
                                confidence: {
                                    type: "number",
                                    minimum: 0,
                                    maximum: 1
                                }
                            },
                            required: ["emotion", "confidence"],
                            additionalProperties: false
                        },
                        maxItems: 3
                    },
                    emotional_context: {
                        type: "string"
                    },
                    recommendations: {
                        type: "array",
                        items: {
                            type: "string"
                        },
                        maxItems: 3
                    }
                },
                required: ["primary_emotion", "confidence", "emotional_intensity", "emotional_context"],
                additionalProperties: false
            }
        }
    },

    // Schema for text content analysis
    textAnalysis: {
        type: "json_schema",
        json_schema: {
            name: "text_analysis",
            strict: true,
            schema: {
                type: "object",
                properties: {
                    sentiment: {
                        type: "string",
                        enum: ["very_positive", "positive", "neutral", "negative", "very_negative"]
                    },
                    sentiment_score: {
                        type: "number",
                        minimum: -1,
                        maximum: 1
                    },
                    themes: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                theme: {
                                    type: "string",
                                    enum: ["technology", "work", "personal", "health", "education", "entertainment", "news", "social", "finance", "travel"]
                                },
                                relevance: {
                                    type: "number",
                                    minimum: 0,
                                    maximum: 1
                                },
                                keywords: {
                                    type: "array",
                                    items: {
                                        type: "string"
                                    },
                                    maxItems: 5
                                }
                            },
                            required: ["theme", "relevance"],
                            additionalProperties: false
                        },
                        maxItems: 5
                    },
                    key_concepts: {
                        type: "array",
                        items: {
                            type: "string"
                        },
                        maxItems: 10
                    },
                    emotional_tone: {
                        type: "string",
                        enum: ["enthusiastic", "calm", "frustrated", "curious", "concerned", "excited", "analytical", "casual"]
                    },
                    complexity_level: {
                        type: "string",
                        enum: ["simple", "moderate", "complex", "technical"]
                    },
                    summary: {
                        type: "string"
                    }
                },
                required: ["sentiment", "sentiment_score", "themes", "emotional_tone", "summary"],
                additionalProperties: false
            }
        }
    },

    // Schema for behavioral pattern analysis
    behaviorAnalysis: {
        type: "json_schema",
        json_schema: {
            name: "behavior_analysis",
            strict: true,
            schema: {
                type: "object",
                properties: {
                    engagement_level: {
                        type: "string",
                        enum: ["very_low", "low", "moderate", "high", "very_high"]
                    },
                    attention_pattern: {
                        type: "string",
                        enum: ["focused", "scattered", "browsing", "searching", "deep_reading"]
                    },
                    interaction_style: {
                        type: "string",
                        enum: ["passive", "selective", "active", "intensive"]
                    },
                    session_quality: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    behavioral_insights: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                insight: {
                                    type: "string"
                                },
                                confidence: {
                                    type: "number",
                                    minimum: 0,
                                    maximum: 1
                                },
                                category: {
                                    type: "string",
                                    enum: ["attention", "engagement", "preference", "habit", "mood"]
                                }
                            },
                            required: ["insight", "confidence", "category"],
                            additionalProperties: false
                        },
                        maxItems: 5
                    },
                    recommendations: {
                        type: "array",
                        items: {
                            type: "string"
                        },
                        maxItems: 3
                    }
                },
                required: ["engagement_level", "attention_pattern", "interaction_style", "session_quality"],
                additionalProperties: false
            }
        }
    },

    // Schema for contextual AI responses
    contextualResponse: {
        type: "json_schema",
        json_schema: {
            name: "contextual_response",
            strict: true,
            schema: {
                type: "object",
                properties: {
                    response: {
                        type: "string"
                    },
                    emotional_awareness: {
                        type: "object",
                        properties: {
                            detected_user_state: {
                                type: "string",
                                enum: ["calm", "stressed", "excited", "focused", "distracted", "curious", "frustrated"]
                            },
                            empathy_level: {
                                type: "number",
                                minimum: 0,
                                maximum: 10
                            },
                            tone_adjustment: {
                                type: "string",
                                enum: ["supportive", "encouraging", "informative", "casual", "professional"]
                            }
                        },
                        required: ["detected_user_state", "empathy_level", "tone_adjustment"],
                        additionalProperties: false
                    },
                    suggested_actions: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                action: {
                                    type: "string"
                                },
                                priority: {
                                    type: "string",
                                    enum: ["low", "medium", "high"]
                                },
                                category: {
                                    type: "string",
                                    enum: ["wellbeing", "productivity", "learning", "social", "health"]
                                }
                            },
                            required: ["action", "priority", "category"],
                            additionalProperties: false
                        },
                        maxItems: 3
                    },
                    confidence: {
                        type: "number",
                        minimum: 0,
                        maximum: 1
                    }
                },
                required: ["response", "emotional_awareness", "confidence"],
                additionalProperties: false
            }
        }
    }
};

module.exports = schemas;
