// Multi-LLM Configuration for HRA Local AI Server
// Supports multiple LLM providers and models

const axios = require('axios');

class LLMManager {
    constructor() {
        this.providers = {
            lmstudio: {
                name: 'LM Studio',
                baseUrl: 'http://localhost:1234/v1',
                models: ['gemma-3-4b-it@q4_k_m'],
                supportsStructuredOutput: true,
                priority: 1,
                enabled: true
            },
            ollama: {
                name: 'Ollama',
                baseUrl: 'http://localhost:11434/v1',
                models: ['llama3.2', 'mistral', 'codellama'],
                supportsStructuredOutput: false,
                priority: 2,
                enabled: false
            },
            openai: {
                name: 'OpenAI',
                baseUrl: 'https://api.openai.com/v1',
                models: ['gpt-4', 'gpt-3.5-turbo'],
                supportsStructuredOutput: true,
                priority: 3,
                enabled: false,
                requiresApiKey: true,
                apiKey: process.env.OPENAI_API_KEY
            },
            anthropic: {
                name: 'Anthrop<PERSON> <PERSON>',
                baseUrl: 'https://api.anthropic.com/v1',
                models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
                supportsStructuredOutput: false,
                priority: 4,
                enabled: false,
                requiresApiKey: true,
                apiKey: process.env.ANTHROPIC_API_KEY
            },
            localai: {
                name: 'LocalAI',
                baseUrl: 'http://localhost:8080/v1',
                models: ['gpt-3.5-turbo', 'llama2'],
                supportsStructuredOutput: false,
                priority: 5,
                enabled: false
            }
        };
        
        this.activeProvider = null;
        this.fallbackChain = [];
        this.initialize();
    }

    async initialize() {
        console.log('🤖 Initializing LLM Manager...');
        
        // Test all enabled providers
        await this.testAllProviders();
        
        // Set up fallback chain based on priority and availability
        this.setupFallbackChain();
        
        console.log(`✅ LLM Manager initialized. Active provider: ${this.activeProvider?.name || 'None'}`);
    }

    async testAllProviders() {
        for (const [key, provider] of Object.entries(this.providers)) {
            if (provider.enabled) {
                const isAvailable = await this.testProvider(key);
                provider.available = isAvailable;
                console.log(`${provider.name}: ${isAvailable ? '✅ Available' : '❌ Unavailable'}`);
            } else {
                provider.available = false;
            }
        }
    }

    async testProvider(providerKey) {
        const provider = this.providers[providerKey];
        
        try {
            // Test different endpoints based on provider
            let testUrl;
            let headers = { 'Content-Type': 'application/json' };
            
            switch (providerKey) {
                case 'lmstudio':
                case 'ollama':
                case 'localai':
                    testUrl = `${provider.baseUrl}/models`;
                    break;
                case 'openai':
                    testUrl = `${provider.baseUrl}/models`;
                    if (provider.apiKey) {
                        headers['Authorization'] = `Bearer ${provider.apiKey}`;
                    } else {
                        return false; // No API key
                    }
                    break;
                case 'anthropic':
                    // Anthropic doesn't have a models endpoint, so we'll test with a simple request
                    return provider.apiKey ? true : false;
                default:
                    return false;
            }
            
            const response = await axios.get(testUrl, { 
                headers, 
                timeout: 5000 
            });
            
            return response.status === 200;
        } catch (error) {
            console.log(`${provider.name} test failed:`, error.message);
            return false;
        }
    }

    setupFallbackChain() {
        // Sort providers by priority and availability
        this.fallbackChain = Object.entries(this.providers)
            .filter(([key, provider]) => provider.enabled && provider.available)
            .sort(([, a], [, b]) => a.priority - b.priority)
            .map(([key, provider]) => ({ key, ...provider }));
        
        // Set active provider to the first available one
        this.activeProvider = this.fallbackChain[0] || null;
        
        console.log('Fallback chain:', this.fallbackChain.map(p => p.name));
    }

    async processRequest(message, context = {}, schemaType = null) {
        for (const provider of this.fallbackChain) {
            try {
                console.log(`Trying ${provider.name}...`);
                const result = await this.callProvider(provider, message, context, schemaType);
                console.log(`✅ Success with ${provider.name}`);
                return result;
            } catch (error) {
                console.log(`❌ ${provider.name} failed:`, error.message);
                continue;
            }
        }
        
        throw new Error('All LLM providers failed');
    }

    async callProvider(provider, message, context, schemaType) {
        const systemPrompt = this.buildSystemPrompt(context, schemaType);
        
        switch (provider.key) {
            case 'lmstudio':
            case 'ollama':
            case 'localai':
                return await this.callOpenAICompatible(provider, message, systemPrompt, schemaType);
            case 'openai':
                return await this.callOpenAI(provider, message, systemPrompt, schemaType);
            case 'anthropic':
                return await this.callAnthropic(provider, message, systemPrompt);
            default:
                throw new Error(`Unknown provider: ${provider.key}`);
        }
    }

    async callOpenAICompatible(provider, message, systemPrompt, schemaType) {
        const requestBody = {
            model: provider.models[0], // Use first available model
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: message }
            ],
            temperature: 0.7,
            max_tokens: 1000,
            stream: false
        };

        // Add structured output if supported
        if (schemaType && provider.supportsStructuredOutput) {
            const schemas = require('./schemas');
            if (schemas[schemaType]) {
                requestBody.response_format = schemas[schemaType];
            }
        }

        const response = await axios.post(`${provider.baseUrl}/chat/completions`, requestBody, {
            timeout: 30000
        });

        const content = response.data.choices[0].message.content;

        // Try to parse JSON if schema was used
        if (schemaType && provider.supportsStructuredOutput) {
            try {
                return JSON.parse(content);
            } catch (error) {
                console.warn('Failed to parse structured output, returning raw content');
                return content;
            }
        }

        return content;
    }

    async callOpenAI(provider, message, systemPrompt, schemaType) {
        const requestBody = {
            model: provider.models[0],
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: message }
            ],
            temperature: 0.7,
            max_tokens: 1000
        };

        // Add structured output for OpenAI
        if (schemaType && provider.supportsStructuredOutput) {
            const schemas = require('./schemas');
            if (schemas[schemaType]) {
                requestBody.response_format = schemas[schemaType];
            }
        }

        const response = await axios.post(`${provider.baseUrl}/chat/completions`, requestBody, {
            headers: {
                'Authorization': `Bearer ${provider.apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        const content = response.data.choices[0].message.content;

        if (schemaType && provider.supportsStructuredOutput) {
            try {
                return JSON.parse(content);
            } catch (error) {
                return content;
            }
        }

        return content;
    }

    async callAnthropic(provider, message, systemPrompt) {
        const requestBody = {
            model: provider.models[0],
            max_tokens: 1000,
            system: systemPrompt,
            messages: [
                { role: "user", content: message }
            ]
        };

        const response = await axios.post(`${provider.baseUrl}/messages`, requestBody, {
            headers: {
                'x-api-key': provider.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            timeout: 30000
        });

        return response.data.content[0].text;
    }

    buildSystemPrompt(context, schemaType) {
        let basePrompt = `You are HRA (Harmonic Resonance Agent), an empathetic AI companion that understands the user's emotional state and behavior patterns.

Current Context:
- Session context: ${JSON.stringify(context)}`;

        if (schemaType) {
            const schemaInstructions = {
                emotionAnalysis: "Analyze the emotional content and provide a structured emotion analysis.",
                textAnalysis: "Analyze the text content for sentiment, themes, and key concepts.",
                behaviorAnalysis: "Analyze behavioral patterns and engagement levels.",
                contextualResponse: "Provide an empathetic response that takes into account the user's emotional state.",
                profileUpdate: "Update the user profile based on recent interactions."
            };

            basePrompt += `\n\nTask: ${schemaInstructions[schemaType] || 'Provide a structured response.'}`;
            
            // Only add JSON instruction if provider supports structured output
            if (this.activeProvider?.supportsStructuredOutput) {
                basePrompt += `\n\nIMPORTANT: Respond ONLY with valid JSON that matches the required schema.`;
            }
        } else {
            basePrompt += `\n\nRespond with empathy and understanding.`;
        }

        return basePrompt;
    }

    // Configuration methods
    enableProvider(providerKey) {
        if (this.providers[providerKey]) {
            this.providers[providerKey].enabled = true;
            console.log(`Enabled ${this.providers[providerKey].name}`);
            this.testAllProviders().then(() => this.setupFallbackChain());
        }
    }

    disableProvider(providerKey) {
        if (this.providers[providerKey]) {
            this.providers[providerKey].enabled = false;
            console.log(`Disabled ${this.providers[providerKey].name}`);
            this.setupFallbackChain();
        }
    }

    setApiKey(providerKey, apiKey) {
        if (this.providers[providerKey] && this.providers[providerKey].requiresApiKey) {
            this.providers[providerKey].apiKey = apiKey;
            console.log(`API key set for ${this.providers[providerKey].name}`);
        }
    }

    getStatus() {
        return {
            activeProvider: this.activeProvider?.name || 'None',
            availableProviders: this.fallbackChain.map(p => p.name),
            allProviders: Object.entries(this.providers).map(([key, provider]) => ({
                key,
                name: provider.name,
                enabled: provider.enabled,
                available: provider.available || false,
                priority: provider.priority
            }))
        };
    }
}

module.exports = LLMManager;
