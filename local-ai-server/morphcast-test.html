<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRA MorphCast Integration Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .content {
            padding: 30px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .emotion-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e9ecef;
        }

        .emotion-panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .emotion-value {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .emotion-name {
            font-weight: 600;
            color: #495057;
        }

        .emotion-score {
            font-weight: bold;
            color: #667eea;
            font-size: 16px;
        }

        .status-panel {
            grid-column: 1 / -1;
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }

        .status-panel.error {
            background: #f8e8e8;
            border-color: #dc3545;
        }

        .status-panel.warning {
            background: #fff3cd;
            border-color: #ffc107;
        }

        .controls {
            grid-column: 1 / -1;
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .logs {
            grid-column: 1 / -1;
            background: #212529;
            color: #28a745;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #6c757d;
        }

        .log-emotion {
            color: #ffc107;
        }

        .log-data {
            color: #17a2b8;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .recording {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 HRA MorphCast Integration</h1>
            <p>Advanced Emotion Detection & Analysis System</p>
        </div>

        <div class="content">
            <div class="emotion-panel">
                <h3>😊 Basic Emotions</h3>
                <div id="basicEmotions">
                    <div class="emotion-value">
                        <span class="emotion-name">Joy</span>
                        <span class="emotion-score" id="joy">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Sadness</span>
                        <span class="emotion-score" id="sadness">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Anger</span>
                        <span class="emotion-score" id="anger">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Fear</span>
                        <span class="emotion-score" id="fear">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Surprise</span>
                        <span class="emotion-score" id="surprise">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Disgust</span>
                        <span class="emotion-score" id="disgust">0%</span>
                    </div>
                </div>
            </div>

            <div class="emotion-panel">
                <h3>⚡ Advanced Metrics</h3>
                <div id="advancedMetrics">
                    <div class="emotion-value">
                        <span class="emotion-name">Arousal</span>
                        <span class="emotion-score" id="arousal">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Valence</span>
                        <span class="emotion-score" id="valence">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Attention</span>
                        <span class="emotion-score" id="attention">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Positivity</span>
                        <span class="emotion-score" id="positivity">0%</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Age</span>
                        <span class="emotion-score" id="age">--</span>
                    </div>
                    <div class="emotion-value">
                        <span class="emotion-name">Gender</span>
                        <span class="emotion-score" id="gender">--</span>
                    </div>
                </div>
            </div>

            <div class="status-panel" id="statusPanel">
                <h3>🔴 MorphCast Not Started</h3>
                <p>Click "Start Detection" to begin emotion analysis</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" id="startBtn">Start Detection</button>
                <button class="btn btn-danger" id="stopBtn" disabled>Stop Detection</button>
                <button class="btn btn-primary" id="sendToHRA">Send to HRA Server</button>
            </div>

            <div class="logs" id="logs">
                <div class="log-entry">
                    <span class="log-timestamp">[Ready]</span> MorphCast Integration Ready - Click Start to begin
                </div>
            </div>
        </div>
    </div>

    <!-- MorphCast SDK Scripts -->
    <script src="https://sdk.morphcast.com/mphtools/v1.1/mphtools.js" data-config="cameraPrivacyPopup, compatibilityUI, compatibilityAutoCheck"></script>
    <script src="https://ai-sdk.morphcast.com/v1.16/ai-sdk.js"></script>
    <script src="https://sdk.morphcast.com/emotion-statistics/v1.0-beta/script.js"></script>
    <script type="module" src="https://sdk.morphcast.com/mphoverlay/v0.1/mph-ai-overlay.js"></script>

    <script>
        class HRAMorphCastIntegration {
            constructor() {
                this.isRunning = false;
                this.currentEmotionData = {};
                this.statisticsUploader = null;
                this.startTime = null;
                this.maxDuration = 900000; // 15 minutes
                
                this.setupUI();
                this.setupMorphCast();
            }

            setupUI() {
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.sendBtn = document.getElementById('sendToHRA');
                this.statusPanel = document.getElementById('statusPanel');
                this.progressFill = document.getElementById('progressFill');
                this.logs = document.getElementById('logs');

                this.startBtn.addEventListener('click', () => this.startDetection());
                this.stopBtn.addEventListener('click', () => this.stopDetection());
                this.sendBtn.addEventListener('click', () => this.sendToHRAServer());
            }

            setupMorphCast() {
                // Configure privacy popup
                MphTools.CameraPrivacyPopup.setText({
                    "title": "Allow HRA to use your camera",
                    "description": "HRA uses your camera for emotion detection to provide personalized AI responses. Your data stays private and is processed locally.",
                    "url": "http://localhost:9876/privacy"
                });

                // Configure statistics uploader
                const statsConfig = {
                    sendDatainterval: 5000,
                    tickInterval: 1000,
                    stopAfter: this.maxDuration,
                    licenseKey: "sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b"
                };
                this.statisticsUploader = new MorphCastStatistics.StatisticsUploader(statsConfig);
            }

            async startDetection() {
                try {
                    this.log('Starting MorphCast emotion detection...', 'info');
                    this.updateStatus('🟡 Initializing...', 'warning');
                    
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;

                    // Configure and load MorphCast
                    const { start, stop } = await CY.loader()
                        .licenseKey("sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b")
                        .addModule(CY.modules().FACE_AROUSAL_VALENCE.name, {smoothness: 0.70})
                        .addModule(CY.modules().FACE_EMOTION.name, {smoothness: 0.40})
                        .addModule(CY.modules().FACE_ATTENTION.name, {smoothness: 0.83})
                        .addModule(CY.modules().ALARM_LOW_ATTENTION.name, {timeWindowMs: 5000, initialToleranceMs: 7000, threshold: 0.33})
                        .addModule(CY.modules().FACE_WISH.name, {smoothness: 0.8})
                        .addModule(CY.modules().FACE_POSE.name, {smoothness: 0.65})
                        .addModule(CY.modules().FACE_AGE.name, {rawOutput: false})
                        .addModule(CY.modules().FACE_GENDER.name, {smoothness: 0.95, threshold: 0.70})
                        .addModule(CY.modules().FACE_FEATURES.name, {smoothness: 0.90})
                        .addModule(CY.modules().FACE_DETECTOR.name, {maxInputFrameSize: 320, smoothness: 0.83})
                        .addModule(CY.modules().DATA_AGGREGATOR.name, {initialWaitMs: 2000, periodMs: 1000})
                        .addModule(CY.modules().FACE_POSITIVITY.name, {smoothness: 0.40, gain: 2, angle: 17})
                        .load();

                    this.stopFunction = stop;
                    
                    // Start detection
                    await start();
                    await this.statisticsUploader.start();
                    
                    this.isRunning = true;
                    this.startTime = Date.now();
                    this.updateStatus('🟢 Detecting Emotions', 'success');
                    this.log('MorphCast started successfully!', 'success');
                    
                    // Auto-stop after max duration
                    setTimeout(() => {
                        if (this.isRunning) {
                            this.stopDetection();
                        }
                    }, this.maxDuration);

                    // Start progress tracking
                    this.startProgressTracking();

                } catch (error) {
                    this.log(`Error starting MorphCast: ${error.message}`, 'error');
                    this.updateStatus('🔴 Error Starting Detection', 'error');
                    this.startBtn.disabled = false;
                    this.stopBtn.disabled = true;
                }
            }

            async stopDetection() {
                try {
                    this.log('Stopping MorphCast...', 'info');
                    
                    if (this.statisticsUploader) {
                        await this.statisticsUploader.stop();
                    }
                    
                    if (this.stopFunction) {
                        await this.stopFunction();
                    }
                    
                    this.isRunning = false;
                    this.updateStatus('🔴 Detection Stopped', 'error');
                    this.log('MorphCast stopped successfully', 'info');
                    
                    this.startBtn.disabled = false;
                    this.stopBtn.disabled = true;
                    this.progressFill.style.width = '0%';
                    
                } catch (error) {
                    this.log(`Error stopping MorphCast: ${error.message}`, 'error');
                }
            }

            startProgressTracking() {
                const updateProgress = () => {
                    if (!this.isRunning) return;
                    
                    const elapsed = Date.now() - this.startTime;
                    const progress = Math.min((elapsed / this.maxDuration) * 100, 100);
                    this.progressFill.style.width = `${progress}%`;
                    
                    if (progress < 100) {
                        setTimeout(updateProgress, 1000);
                    }
                };
                updateProgress();
            }

            updateStatus(message, type) {
                this.statusPanel.className = `status-panel ${type}`;
                this.statusPanel.innerHTML = `
                    <h3>${message}</h3>
                    <p>${type === 'success' ? 'Real-time emotion analysis active' : 
                         type === 'warning' ? 'Preparing camera and modules...' : 
                         'Click Start Detection to begin'}</p>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: ${this.progressFill?.style.width || '0%'}"></div>
                    </div>
                `;
                this.progressFill = document.getElementById('progressFill');
            }

            updateEmotionDisplay(data) {
                // Update basic emotions
                if (data.emotion) {
                    Object.entries(data.emotion).forEach(([emotion, value]) => {
                        const element = document.getElementById(emotion.toLowerCase());
                        if (element) {
                            element.textContent = `${Math.round(value * 100)}%`;
                        }
                    });
                }

                // Update advanced metrics
                if (data.arousal !== undefined) {
                    document.getElementById('arousal').textContent = `${Math.round(data.arousal * 100)}%`;
                }
                if (data.valence !== undefined) {
                    document.getElementById('valence').textContent = `${Math.round(data.valence * 100)}%`;
                }
                if (data.attention !== undefined) {
                    document.getElementById('attention').textContent = `${Math.round(data.attention * 100)}%`;
                }
                if (data.positivity !== undefined) {
                    document.getElementById('positivity').textContent = `${Math.round(data.positivity * 100)}%`;
                }
                if (data.age !== undefined) {
                    document.getElementById('age').textContent = Math.round(data.age);
                }
                if (data.gender !== undefined) {
                    document.getElementById('gender').textContent = data.gender > 0.5 ? 'Male' : 'Female';
                }

                // Store current data
                this.currentEmotionData = { ...this.currentEmotionData, ...data };
                
                // AUTO-STREAM TO HRA SERVER (NEW!)
                this.streamToHRAServer(data);
            }

            async streamToHRAServer(emotionData) {
                if (!this.isRunning) return; // Only stream when actively detecting
                
                try {
                    // Send real-time emotion data to HRA streaming endpoint
                    const response = await fetch('/api/stream/emotion-update', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            type: 'emotion_stream',
                            data: emotionData,
                            timestamp: new Date().toISOString(),
                            source: 'morphcast_realtime'
                        })
                    });
                    
                    if (response.ok) {
                        // Silently stream (don't log every update to avoid spam)
                        // this.log('Emotion streamed to HRA', 'data');
                    }
                } catch (error) {
                    // Silently fail for real-time streaming to avoid log spam
                    console.warn('Real-time emotion streaming error:', error);
                }
            }

            async sendToHRAServer() {
                if (Object.keys(this.currentEmotionData).length === 0) {
                    this.log('No emotion data to send', 'warning');
                    return;
                }

                try {
                    const response = await fetch('http://localhost:9876/api/analyze-emotion', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            emotionData: this.currentEmotionData,
                            context: {
                                source: 'morphcast_test',
                                timestamp: new Date().toISOString(),
                                sessionDuration: this.isRunning ? Date.now() - this.startTime : 0
                            }
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        this.log('Emotion data sent to HRA server successfully', 'success');
                        this.log(`Server response: ${JSON.stringify(result.analysis)}`, 'data');
                    } else {
                        throw new Error(`Server responded with ${response.status}`);
                    }
                } catch (error) {
                    this.log(`Error sending to HRA server: ${error.message}`, 'error');
                }
            }

            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                let icon = '';
                switch (type) {
                    case 'success': icon = '✅'; break;
                    case 'error': icon = '❌'; break;
                    case 'warning': icon = '⚠️'; break;
                    case 'data': icon = '📊'; break;
                    default: icon = 'ℹ️';
                }
                
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span> 
                    ${icon} <span class="log-${type}">${message}</span>
                `;
                
                this.logs.appendChild(logEntry);
                this.logs.scrollTop = this.logs.scrollHeight;
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const integration = new HRAMorphCastIntegration();

            // Set up MorphCast event listeners
            window.addEventListener(CY.modules().FACE_EMOTION.eventName, (evt) => {
                integration.updateEmotionDisplay({ emotion: evt.detail });
                integration.log(`Emotions: ${Object.entries(evt.detail).map(([k,v]) => `${k}:${Math.round(v*100)}%`).join(', ')}`, 'emotion');
            });

            window.addEventListener(CY.modules().FACE_AROUSAL_VALENCE.eventName, (evt) => {
                integration.updateEmotionDisplay({ arousal: evt.detail.arousal, valence: evt.detail.valence });
            });

            window.addEventListener(CY.modules().FACE_ATTENTION.eventName, (evt) => {
                integration.updateEmotionDisplay({ attention: evt.detail.attention });
            });

            window.addEventListener(CY.modules().FACE_POSITIVITY.eventName, (evt) => {
                integration.updateEmotionDisplay({ positivity: evt.detail.positivity });
            });

            window.addEventListener(CY.modules().FACE_AGE.eventName, (evt) => {
                integration.updateEmotionDisplay({ age: evt.detail.age });
            });

            window.addEventListener(CY.modules().FACE_GENDER.eventName, (evt) => {
                integration.updateEmotionDisplay({ gender: evt.detail.gender });
            });

            window.addEventListener(CY.modules().ALARM_LOW_ATTENTION.eventName, (evt) => {
                integration.log('⚠️ Low attention detected!', 'warning');
            });

            window.addEventListener(CY.modules().DATA_AGGREGATOR.eventName, (evt) => {
                integration.log(`Data aggregated: ${Object.keys(evt.detail).length} metrics`, 'data');
            });
        });
    </script>
</body>
</html>
