// Convert SVG icons to PNG using Sharp
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function convertSvgToPng() {
    const iconsDir = path.join(__dirname, '..', 'icons');
    const sizes = [16, 48, 128];
    
    for (const size of sizes) {
        const svgPath = path.join(iconsDir, `icon${size}.svg`);
        const pngPath = path.join(iconsDir, `icon${size}.png`);
        
        if (fs.existsSync(svgPath)) {
            try {
                await sharp(svgPath)
                    .resize(size, size)
                    .png()
                    .toFile(pngPath);
                
                console.log(`Converted icon${size}.svg to icon${size}.png`);
            } catch (error) {
                console.error(`Error converting icon${size}.svg:`, error.message);
            }
        }
    }
    
    console.log('PNG conversion completed!');
}

convertSvgToPng().catch(console.error);
