// HRA Chrome Extension - Popup Script
// Handles popup UI interactions, module toggles, and real-time updates

class HRAPopup {
    constructor() {
        this.isConnected = false;
        this.sessionStats = {
            selections: 0,
            copies: 0,
            focusPercentage: 100,
            scrollDepth: 0,
            clicks: 0
        };
        
        this.modules = {
            emotion: false,
            text: true,
            behavioral: true
        };
        
        this.initialize();
    }

    async initialize() {
        try {
            // Load saved preferences and states
            await this.loadState();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize UI
            this.updateUI();
            
            // Check LAS connection status
            this.checkConnectionStatus();
            
            // Start real-time updates
            this.startRealTimeUpdates();
            
            console.log('HRA Popup initialized');
        } catch (error) {
            console.error('Failed to initialize popup:', error);
        }
    }

    async loadState() {
        return new Promise((resolve) => {
            chrome.storage.local.get([
                'modules',
                'sessionStats',
                'userPreferences',
                'lasConnectionStatus'
            ], (result) => {
                if (result.modules) {
                    this.modules = { ...this.modules, ...result.modules };
                }
                if (result.sessionStats) {
                    this.sessionStats = { ...this.sessionStats, ...result.sessionStats };
                }
                if (result.lasConnectionStatus) {
                    this.isConnected = result.lasConnectionStatus === 'connected';
                }
                resolve();
            });
        });
    }

    setupEventListeners() {
        // Module toggles
        document.getElementById('emotionToggle').addEventListener('change', (e) => {
            this.toggleModule('emotion', e.target.checked);
        });

        document.getElementById('textToggle').addEventListener('change', (e) => {
            this.toggleModule('text', e.target.checked);
        });

        document.getElementById('behavioralToggle').addEventListener('change', (e) => {
            this.toggleModule('behavioral', e.target.checked);
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Settings button
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.openSettings();
        });

        // Dashboard link
        document.getElementById('openDashboard').addEventListener('click', (e) => {
            e.preventDefault();
            this.openDashboard();
        });

        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
        });
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.type) {
            case 'connectionStatusUpdate':
                this.updateConnectionStatus(request.status);
                break;
            case 'sessionStatsUpdate':
                this.updateSessionStats(request.stats);
                break;
            case 'emotionDataUpdate':
                this.updateEmotionDisplay(request.data);
                break;
            default:
                console.log('Unknown message:', request.type);
        }
    }

    async toggleModule(moduleName, enabled) {
        this.modules[moduleName] = enabled;
        
        // Save to storage
        chrome.storage.local.set({ modules: this.modules });
        
        // Send message to background script
        chrome.runtime.sendMessage({
            type: 'toggleModule',
            module: moduleName === 'emotion' ? 'morphcast' : moduleName,
            enabled: enabled
        });
        
        // Update UI
        this.updateModuleStatus(moduleName, enabled);
        
        console.log(`Module ${moduleName} ${enabled ? 'enabled' : 'disabled'}`);
    }

    updateModuleStatus(moduleName, enabled) {
        const statusElements = {
            text: document.getElementById('textStatus'),
            behavioral: document.getElementById('behavioralStatus')
        };
        
        if (statusElements[moduleName]) {
            statusElements[moduleName].textContent = enabled ? 
                (moduleName === 'text' ? 'Tracking text selections' : 'Monitoring engagement') :
                'Disabled';
        }
        
        // Update emotion module display
        if (moduleName === 'emotion') {
            const emotionContent = document.getElementById('emotionContent');
            if (enabled) {
                emotionContent.innerHTML = `
                    <div class="emotion-active">
                        <div class="emotion-indicator pulse"></div>
                        <div class="emotion-status">Analyzing emotions...</div>
                    </div>
                `;
            } else {
                emotionContent.innerHTML = `
                    <svg class="icon" width="40" height="40" viewBox="0 0 24 24">
                        <circle cx="12" cy="8" r="7"></circle>
                        <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                    </svg>
                    <div class="emotion-placeholder-text">Enable emotion detection to start capturing your emotional resonance</div>
                `;
            }
        }
    }

    updateUI() {
        // Set toggle states
        document.getElementById('emotionToggle').checked = this.modules.emotion;
        document.getElementById('textToggle').checked = this.modules.text;
        document.getElementById('behavioralToggle').checked = this.modules.behavioral;
        
        // Update module statuses
        Object.keys(this.modules).forEach(module => {
            this.updateModuleStatus(module, this.modules[module]);
        });
        
        // Update session stats
        this.updateSessionStatsDisplay();
        
        // Update connection indicator
        this.updateConnectionIndicator();
    }

    updateSessionStatsDisplay() {
        document.getElementById('selectionCount').textContent = this.sessionStats.selections;
        document.getElementById('copyCount').textContent = this.sessionStats.copies;
        document.getElementById('focusPercentage').textContent = `${this.sessionStats.focusPercentage}%`;
        document.getElementById('scrollDepth').textContent = `${this.sessionStats.scrollDepth}%`;
        document.getElementById('clickCount').textContent = this.sessionStats.clicks;
    }

    updateSessionStats(stats) {
        this.sessionStats = { ...this.sessionStats, ...stats };
        this.updateSessionStatsDisplay();
        
        // Save to storage
        chrome.storage.local.set({ sessionStats: this.sessionStats });
    }

    updateConnectionStatus(status) {
        this.isConnected = status === 'connected';
        this.updateConnectionIndicator();
    }

    updateConnectionIndicator() {
        const indicator = document.querySelector('.connection-status');
        if (indicator) {
            indicator.className = `connection-status ${this.isConnected ? 'connected' : 'disconnected'}`;
            indicator.textContent = this.isConnected ? 'Connected to LAS' : 'Disconnected';
        }
    }

    async checkConnectionStatus() {
        try {
            const response = await chrome.runtime.sendMessage({ type: 'getLASStatus' });
            this.updateConnectionStatus(response.connected ? 'connected' : 'disconnected');
        } catch (error) {
            console.error('Failed to check connection status:', error);
            this.updateConnectionStatus('disconnected');
        }
    }

    startRealTimeUpdates() {
        // Update stats every 2 seconds
        setInterval(() => {
            this.requestStatsUpdate();
        }, 2000);
    }

    requestStatsUpdate() {
        chrome.runtime.sendMessage({ type: 'getSessionStats' });
    }

    updateEmotionDisplay(emotionData) {
        if (!this.modules.emotion) return;
        
        const emotionContent = document.getElementById('emotionContent');
        if (emotionData && emotionData.emotions) {
            const topEmotion = Object.entries(emotionData.emotions)
                .sort(([,a], [,b]) => b - a)[0];
            
            emotionContent.innerHTML = `
                <div class="emotion-active">
                    <div class="emotion-value">${topEmotion[0]}</div>
                    <div class="emotion-confidence">${Math.round(topEmotion[1] * 100)}%</div>
                    <div class="emotion-timestamp">${new Date().toLocaleTimeString()}</div>
                </div>
            `;
        }
    }

    toggleTheme() {
        document.body.classList.toggle('dark-theme');
        const isDark = document.body.classList.contains('dark-theme');
        chrome.storage.local.set({ darkTheme: isDark });
    }

    openSettings() {
        chrome.tabs.create({ url: chrome.runtime.getURL('settings.html') });
    }

    openDashboard() {
        chrome.tabs.create({ url: 'http://localhost:8080/dashboard' });
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HRAPopup();
});
