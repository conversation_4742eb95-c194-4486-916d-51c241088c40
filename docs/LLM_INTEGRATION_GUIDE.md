# HRA LLM Integration Guide

## 🎯 Overview

The Harmonic Resonance Agent (HRA) provides a real-time streaming architecture that enables Large Language Models (LLMs) to receive continuous context updates about user emotions, behavior patterns, and activity. This creates truly context-aware AI interactions.

## 🚀 Quick Start

### 1. Subscribe to Real-time Updates

```bash
curl -X POST http://localhost:9876/api/llm/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "callbackUrl": "http://your-llm-server/hra-context",
    "events": ["emotion", "behavior", "summary", "all"]
  }'
```

### 2. Receive Streaming Context

Your LLM server will receive POST requests with real-time context:

```json
{
  "type": "realtime_context_update",
  "category": "emotion",
  "data": {
    "current": {
      "joy": 0.8,
      "sadness": 0.1,
      "arousal": 0.7,
      "valence": 0.8,
      "attention": 0.9
    },
    "trends": {
      "direction": "increasing",
      "magnitude": 0.3,
      "confidence": "high"
    }
  },
  "timestamp": "2025-06-04T12:35:47.342Z",
  "userContext": {
    "emotionalProfile": {...},
    "behaviorPatterns": {...},
    "interests": [...],
    "sessionStats": {...}
  }
}
```

### 3. Query Current State

```bash
# Get current emotional state
curl http://localhost:9876/api/stream/emotions

# Get behavioral patterns
curl http://localhost:9876/api/stream/behavior

# Get complete context summary
curl http://localhost:9876/api/stream/context
```

## 📊 Data Streams

### Emotion Stream (Every 5 seconds)
- **Primary emotions**: joy, sadness, anger, fear, surprise, disgust
- **Advanced metrics**: arousal, valence, attention, positivity
- **Demographics**: age, gender estimation
- **Trends**: emotional stability, intensity changes

### Behavior Stream (Every 15 seconds)
- **Activity patterns**: clicks, scrolls, navigation
- **Engagement metrics**: activity level, focus duration
- **Text interactions**: selections, copies, form inputs
- **Temporal patterns**: usage frequency, session duration

### Context Summary (Every 10 seconds)
- **User profile**: interests, preferences, emotional profile
- **Session statistics**: active time, interaction count
- **Real-time metrics**: current engagement, attention level
- **Pattern analysis**: behavioral trends, emotional stability

## 🔌 Integration Examples

### Python Flask Example

```python
from flask import Flask, request, jsonify
import requests
import json

app = Flask(__name__)

class HRAIntegration:
    def __init__(self):
        self.hra_base_url = "http://localhost:9876"
        self.current_context = {}
        self.subscribe_to_hra()
    
    def subscribe_to_hra(self):
        """Subscribe to HRA real-time updates"""
        subscription_data = {
            "callbackUrl": "http://your-server:5000/hra-context",
            "events": ["emotion", "behavior", "summary"]
        }
        
        response = requests.post(
            f"{self.hra_base_url}/api/llm/subscribe",
            json=subscription_data
        )
        
        if response.status_code == 200:
            print("✅ Subscribed to HRA updates")
        else:
            print("❌ Failed to subscribe to HRA")
    
    def get_current_context(self):
        """Fetch current user context"""
        try:
            response = requests.get(f"{self.hra_base_url}/api/stream/context")
            return response.json()
        except Exception as e:
            print(f"Error fetching context: {e}")
            return {}
    
    def process_context_update(self, update_data):
        """Process incoming context updates"""
        self.current_context.update(update_data)
        
        # Analyze emotional state
        if update_data.get("category") == "emotion":
            emotion_data = update_data.get("data", {})
            self.analyze_emotional_state(emotion_data)
        
        # Analyze behavioral patterns
        elif update_data.get("category") == "behavior":
            behavior_data = update_data.get("data", {})
            self.analyze_behavioral_patterns(behavior_data)
    
    def analyze_emotional_state(self, emotion_data):
        """Analyze user's emotional state for AI responses"""
        current = emotion_data.get("current", {})
        
        if current.get("joy", 0) > 0.7:
            self.emotional_context = "positive_mood"
        elif current.get("sadness", 0) > 0.6:
            self.emotional_context = "needs_support"
        elif current.get("attention", 0) < 0.3:
            self.emotional_context = "low_attention"
        else:
            self.emotional_context = "neutral"
    
    def analyze_behavioral_patterns(self, behavior_data):
        """Analyze user behavior for interaction adaptation"""
        activity = behavior_data.get("activity", {})
        engagement = behavior_data.get("engagement", 0)
        
        if engagement > 80:
            self.interaction_style = "detailed_responses"
        elif engagement < 30:
            self.interaction_style = "brief_engaging"
        else:
            self.interaction_style = "balanced"
    
    def generate_contextual_response(self, user_message):
        """Generate AI response with HRA context"""
        context = self.get_current_context()
        
        # Build context-aware prompt
        prompt = f"""
        User Message: {user_message}
        
        Current Context:
        - Emotional State: {getattr(self, 'emotional_context', 'unknown')}
        - Interaction Style: {getattr(self, 'interaction_style', 'balanced')}
        - Engagement Level: {context.get('sessionStats', {}).get('engagement', 'unknown')}
        - Recent Activity: {context.get('behaviorPatterns', {})}
        
        Respond appropriately considering the user's current emotional and behavioral state.
        """
        
        # Send to your LLM (OpenAI, Anthropic, etc.)
        return self.call_llm(prompt)
    
    def call_llm(self, prompt):
        """Call your preferred LLM service"""
        # Implement your LLM call here
        # This could be OpenAI, Anthropic, local model, etc.
        return "Context-aware response based on HRA data"

# Initialize HRA integration
hra = HRAIntegration()

@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    """Endpoint to receive HRA context updates"""
    update_data = request.json
    hra.process_context_update(update_data)
    return jsonify({"status": "received"})

@app.route('/chat', methods=['POST'])
def chat_endpoint():
    """Chat endpoint with HRA context integration"""
    user_message = request.json.get('message')
    response = hra.generate_contextual_response(user_message)
    return jsonify({"response": response})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### Node.js Express Example

```javascript
const express = require('express');
const axios = require('axios');
const app = express();

app.use(express.json());

class HRAIntegration {
    constructor() {
        this.hraBaseUrl = 'http://localhost:9876';
        this.currentContext = {};
        this.subscribeToHRA();
    }
    
    async subscribeToHRA() {
        try {
            const response = await axios.post(`${this.hraBaseUrl}/api/llm/subscribe`, {
                callbackUrl: 'http://your-server:3000/hra-context',
                events: ['emotion', 'behavior', 'summary']
            });
            console.log('✅ Subscribed to HRA updates');
        } catch (error) {
            console.error('❌ Failed to subscribe to HRA:', error.message);
        }
    }
    
    async getCurrentContext() {
        try {
            const response = await axios.get(`${this.hraBaseUrl}/api/stream/context`);
            return response.data;
        } catch (error) {
            console.error('Error fetching context:', error.message);
            return {};
        }
    }
    
    processContextUpdate(updateData) {
        this.currentContext = { ...this.currentContext, ...updateData };
        
        if (updateData.category === 'emotion') {
            this.analyzeEmotionalState(updateData.data);
        } else if (updateData.category === 'behavior') {
            this.analyzeBehavioralPatterns(updateData.data);
        }
    }
    
    analyzeEmotionalState(emotionData) {
        const current = emotionData.current || {};
        
        if (current.joy > 0.7) {
            this.emotionalContext = 'positive_mood';
        } else if (current.sadness > 0.6) {
            this.emotionalContext = 'needs_support';
        } else if (current.attention < 0.3) {
            this.emotionalContext = 'low_attention';
        } else {
            this.emotionalContext = 'neutral';
        }
    }
    
    analyzeBehavioralPatterns(behaviorData) {
        const engagement = behaviorData.engagement || 0;
        
        if (engagement > 80) {
            this.interactionStyle = 'detailed_responses';
        } else if (engagement < 30) {
            this.interactionStyle = 'brief_engaging';
        } else {
            this.interactionStyle = 'balanced';
        }
    }
    
    async generateContextualResponse(userMessage) {
        const context = await this.getCurrentContext();
        
        const prompt = `
        User Message: ${userMessage}
        
        Current Context:
        - Emotional State: ${this.emotionalContext || 'unknown'}
        - Interaction Style: ${this.interactionStyle || 'balanced'}
        - Engagement Level: ${context.sessionStats?.engagement || 'unknown'}
        - Behavior Patterns: ${JSON.stringify(context.behaviorPatterns || {})}
        
        Respond appropriately considering the user's current emotional and behavioral state.
        `;
        
        return await this.callLLM(prompt);
    }
    
    async callLLM(prompt) {
        // Implement your LLM call here
        // This could be OpenAI, Anthropic, local model, etc.
        return 'Context-aware response based on HRA data';
    }
}

const hra = new HRAIntegration();

// Endpoint to receive HRA context updates
app.post('/hra-context', (req, res) => {
    hra.processContextUpdate(req.body);
    res.json({ status: 'received' });
});

// Chat endpoint with HRA context integration
app.post('/chat', async (req, res) => {
    const userMessage = req.body.message;
    const response = await hra.generateContextualResponse(userMessage);
    res.json({ response });
});

app.listen(3000, () => {
    console.log('LLM server with HRA integration running on port 3000');
});
```

## 🎭 Emotion-Aware AI Responses

### Emotional State Mapping

```python
def adapt_response_to_emotion(emotion_data, base_response):
    """Adapt AI response based on user's emotional state"""
    current = emotion_data.get('current', {})
    
    # High joy - enthusiastic responses
    if current.get('joy', 0) > 0.7:
        return f"That's wonderful! {base_response} I'm excited to help you with this!"
    
    # High sadness - supportive responses
    elif current.get('sadness', 0) > 0.6:
        return f"I understand this might be challenging. {base_response} I'm here to support you."
    
    # Low attention - concise responses
    elif current.get('attention', 0) < 0.3:
        return f"Quick answer: {base_response[:100]}... Would you like me to elaborate?"
    
    # High stress - calming responses
    elif current.get('arousal', 0) > 0.8 and current.get('valence', 0) < 0.4:
        return f"Let's take this step by step. {base_response} Take your time."
    
    return base_response
```

### Behavioral Adaptation

```python
def adapt_to_behavior_patterns(behavior_data, response_style):
    """Adapt interaction style based on behavior patterns"""
    engagement = behavior_data.get('engagement', 0)
    activity = behavior_data.get('activity', {})
    
    if engagement > 80:
        # High engagement - detailed, comprehensive responses
        return 'detailed'
    elif engagement < 30:
        # Low engagement - brief, engaging responses
        return 'brief'
    elif activity.get('intensity') == 'high':
        # High activity - quick, actionable responses
        return 'actionable'
    else:
        return 'balanced'
```

## 📈 Advanced Integration Patterns

### Memory Integration with Mem0

```python
import mem0

class HRAMemoryIntegration:
    def __init__(self):
        self.mem0_client = mem0.Client()
        self.user_id = "hra_user"
    
    def store_emotional_pattern(self, emotion_data):
        """Store emotional patterns in long-term memory"""
        pattern = {
            "type": "emotional_pattern",
            "data": emotion_data,
            "timestamp": datetime.now().isoformat()
        }
        
        self.mem0_client.add_memory(
            content=f"User emotional pattern: {json.dumps(pattern)}",
            user_id=self.user_id
        )
    
    def retrieve_emotional_history(self):
        """Retrieve emotional patterns from memory"""
        memories = self.mem0_client.search_memories(
            query="emotional patterns",
            user_id=self.user_id
        )
        return memories
    
    def generate_personalized_response(self, current_context, user_message):
        """Generate response using current context + memory"""
        emotional_history = self.retrieve_emotional_history()
        
        prompt = f"""
        Current Context: {json.dumps(current_context)}
        Emotional History: {json.dumps(emotional_history)}
        User Message: {user_message}
        
        Generate a personalized response considering both current state and historical patterns.
        """
        
        return self.call_llm(prompt)
```

### Multi-LLM Coordination

```python
class MultiLLMCoordinator:
    def __init__(self):
        self.llm_endpoints = {
            'emotional_analysis': 'http://emotion-llm:5000',
            'task_assistance': 'http://task-llm:5001',
            'creative_support': 'http://creative-llm:5002'
        }
        self.current_context = {}
    
    def route_to_appropriate_llm(self, user_message, context):
        """Route requests to specialized LLMs based on context"""
        emotion_data = context.get('emotion', {})
        behavior_data = context.get('behavior', {})
        
        # Route based on emotional state
        if emotion_data.get('sadness', 0) > 0.6:
            return self.call_llm('emotional_analysis', user_message, context)
        
        # Route based on activity type
        elif 'task' in user_message.lower() or behavior_data.get('engagement', 0) > 80:
            return self.call_llm('task_assistance', user_message, context)
        
        # Route to creative support for low-structure activities
        elif behavior_data.get('activity', {}).get('intensity') == 'low':
            return self.call_llm('creative_support', user_message, context)
        
        # Default routing
        return self.call_llm('task_assistance', user_message, context)
```

## 🔧 Configuration Options

### Subscription Events

```json
{
  "events": [
    "emotion",      // Emotional state updates
    "behavior",     // Behavioral pattern updates  
    "summary",      // Context summary updates
    "text",         // Text capture events
    "activity",     // Activity/navigation events
    "all"          // All event types
  ]
}
```

### Update Frequencies

- **Emotion Stream**: 5 seconds (configurable)
- **Behavior Stream**: 15 seconds (configurable)
- **Context Summary**: 10 seconds (configurable)
- **Activity Events**: Real-time (immediate)

### Filtering Options

```python
# Subscribe to specific emotion thresholds
subscription = {
    "callbackUrl": "http://your-server/hra-context",
    "events": ["emotion"],
    "filters": {
        "emotion": {
            "joy_threshold": 0.7,
            "sadness_threshold": 0.6,
            "attention_threshold": 0.3
        }
    }
}
```

## 🚀 Production Deployment

### Docker Integration

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# Environment variables for HRA integration
ENV HRA_BASE_URL=http://hra-server:9876
ENV LLM_CALLBACK_URL=http://your-llm:5000/hra-context

EXPOSE 5000
CMD ["python", "app.py"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hra-integrated-llm
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hra-llm
  template:
    metadata:
      labels:
        app: hra-llm
    spec:
      containers:
      - name: llm-service
        image: your-llm:latest
        env:
        - name: HRA_BASE_URL
          value: "http://hra-service:9876"
        - name: LLM_CALLBACK_URL
          value: "http://hra-llm-service:5000/hra-context"
        ports:
        - containerPort: 5000
```

## 📊 Monitoring & Analytics

### Health Checks

```python
def check_hra_connection():
    """Check HRA server connectivity"""
    try:
        response = requests.get(f"{HRA_BASE_URL}/health")
        return response.status_code == 200
    except:
        return False

def get_subscription_status():
    """Check subscription status"""
    try:
        response = requests.get(f"{HRA_BASE_URL}/api/llm/status")
        return response.json()
    except:
        return {"status": "disconnected"}
```

### Metrics Collection

```python
import time
from collections import defaultdict

class HRAMetrics:
    def __init__(self):
        self.update_counts = defaultdict(int)
        self.response_times = []
        self.context_quality_scores = []
    
    def record_update(self, update_type):
        self.update_counts[update_type] += 1
    
    def record_response_time(self, start_time):
        self.response_times.append(time.time() - start_time)
    
    def get_metrics_summary(self):
        return {
            "updates_received": dict(self.update_counts),
            "avg_response_time": sum(self.response_times) / len(self.response_times),
            "context_quality": sum(self.context_quality_scores) / len(self.context_quality_scores)
        }
```

## 🔒 Security Considerations

### Authentication

```python
import jwt
import hashlib

def verify_hra_webhook(request):
    """Verify webhook authenticity"""
    signature = request.headers.get('X-HRA-Signature')
    payload = request.get_data()
    
    expected_signature = hashlib.sha256(
        (payload + HRA_WEBHOOK_SECRET).encode()
    ).hexdigest()
    
    return signature == expected_signature

def authenticate_llm_request(token):
    """Authenticate LLM requests"""
    try:
        payload = jwt.decode(token, LLM_SECRET_KEY, algorithms=['HS256'])
        return payload.get('llm_id')
    except jwt.InvalidTokenError:
        return None
```

### Rate Limiting

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per hour", "100 per minute"]
)

@app.route('/hra-context', methods=['POST'])
@limiter.limit("60 per minute")
def receive_hra_update():
    # Handle HRA updates with rate limiting
    pass
```

## 🎯 Best Practices

### 1. Context Buffering
```python
class ContextBuffer:
    def __init__(self, max_size=100):
        self.buffer = []
        self.max_size = max_size
    
    def add_context(self, context_data):
        self.buffer.append(context_data)
        if len(self.buffer) > self.max_size:
            self.buffer.pop(0)
    
    def get_context_window(self, window_size=10):
        return self.buffer[-window_size:]
```

### 2. Graceful Degradation
```python
def get_context_with_fallback():
    """Get context with fallback to cached data"""
    try:
        return requests.get(f"{HRA_BASE_URL}/api/stream/context").json()
    except:
        return self.cached_context or {}
```

### 3. Async Processing
```python
import asyncio
import aiohttp

async def process_hra_updates_async():
    """Process HRA updates asynchronously"""
    async with aiohttp.ClientSession() as session:
        while True:
            try:
                async with session.get(f"{HRA_BASE_URL}/api/stream/context") as response:
                    context = await response.json()
                    await self.process_context_async(context)
                await asyncio.sleep(5)
            except Exception as e:
                print(f"Error in async processing: {e}")
                await asyncio.sleep(10)
```

## 📞 Support & Resources

### API Documentation
- **Base URL**: `http://localhost:9876`
- **WebSocket**: `ws://localhost:9876`
- **Health Check**: `GET /health`
- **API Status**: `GET /api/llm/status`

### Community Resources
- **GitHub**: [HRA Integration Examples](https://github.com/hra-project/llm-integrations)
- **Discord**: [HRA Developer Community](https://discord.gg/hra-dev)
- **Documentation**: [Full API Reference](https://docs.hra-project.com)

### Getting Help
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions on Discord
- **Email**: <EMAIL>

---

*This documentation is part of the Harmonic Resonance Agent (HRA) project. For the latest updates and examples, visit our GitHub repository.*
