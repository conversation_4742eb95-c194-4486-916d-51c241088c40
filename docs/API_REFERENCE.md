# HRA API Reference

## 🌐 Base URL
```
http://localhost:9876
```

## 🔌 WebSocket Connection
```
ws://localhost:9876
```

## 📡 Real-time Streaming Endpoints

### Subscribe to Updates
Subscribe your LLM to receive real-time context updates.

**Endpoint:** `POST /api/llm/subscribe`

**Request Body:**
```json
{
  "callbackUrl": "http://your-llm-server/hra-context",
  "events": ["emotion", "behavior", "summary", "all"]
}
```

**Response:**
```json
{
  "subscriberId": "1234567890",
  "message": "Subscribed to real-time updates"
}
```

**Event Types:**
- `emotion` - Emotional state updates (every 5s)
- `behavior` - Behavioral pattern updates (every 15s) 
- `summary` - Context summary updates (every 10s)
- `activity` - Real-time activity events
- `text` - Text capture events
- `all` - All event types

### Get Current Context
Fetch the current user context summary.

**Endpoint:** `GET /api/stream/context`

**Response:**
```json
{
  "emotionalProfile": {
    "dominantEmotion": "joy",
    "stability": "stable",
    "intensity": 0.7
  },
  "behaviorPatterns": {
    "general_behavior": {
      "count": 25,
      "lastSeen": "2025-06-04T12:35:47.342Z"
    }
  },
  "interests": ["technology", "ai", "development"],
  "sessionStats": {
    "emotions": 15,
    "textCaptures": 8,
    "behavioralEvents": 25,
    "activeSessions": 1
  }
}
```

### Get Emotional State
Fetch current emotional state and trends.

**Endpoint:** `GET /api/stream/emotions`

**Response:**
```json
{
  "current": {
    "primary": {
      "joy": 0.8,
      "sadness": 0.1,
      "anger": 0.05,
      "fear": 0.02,
      "surprise": 0.3,
      "disgust": 0.01
    },
    "stability": "stable",
    "intensity": 0.7
  },
  "recent": [
    {
      "timestamp": "2025-06-04T12:35:47.342Z",
      "joy": 0.8,
      "arousal": 0.7,
      "valence": 0.8,
      "attention": 0.9
    }
  ],
  "trends": {
    "direction": "increasing",
    "magnitude": 0.3,
    "confidence": "high"
  }
}
```

### Get Behavioral Patterns
Fetch behavioral patterns and engagement metrics.

**Endpoint:** `GET /api/stream/behavior`

**Response:**
```json
{
  "patterns": {
    "general_behavior": {
      "frequency": 25,
      "lastActivity": "2025-06-04T12:35:47.342Z",
      "trend": "stable"
    }
  },
  "recent": [
    {
      "sessionId": "837d6538-4ae4-4da3-80fc-2184c1d21ebb",
      "timestamp": "2025-06-04T12:35:47.342Z",
      "type": "behavioral_update"
    }
  ],
  "activity": {
    "count": 22,
    "types": ["behavioral_update"],
    "intensity": "high"
  },
  "engagement": 54
}
```

## 🤖 AI Processing Endpoints

### Chat with Context
Send messages to AI with automatic context integration.

**Endpoint:** `POST /api/chat`

**Request Body:**
```json
{
  "message": "How are you feeling today?",
  "context": {
    "source": "llm_integration",
    "includeEmotions": true,
    "includeBehavior": true
  }
}
```

**Response:**
```json
{
  "response": "Based on your current emotional state showing high joy (0.8) and excellent attention (0.9), you seem to be having a great day! Your engagement levels are high, which suggests you're feeling productive and focused.",
  "context": {
    "emotionalState": "positive_mood",
    "engagementLevel": "high",
    "responseStyle": "enthusiastic"
  },
  "timestamp": "2025-06-04T12:35:47.342Z"
}
```

### Analyze Emotions
Send emotion data for AI analysis.

**Endpoint:** `POST /api/analyze-emotion`

**Request Body:**
```json
{
  "emotionData": {
    "joy": 0.8,
    "sadness": 0.1,
    "arousal": 0.7,
    "valence": 0.8,
    "attention": 0.9
  },
  "context": {
    "source": "morphcast_test",
    "timestamp": "2025-06-04T12:35:47.342Z",
    "sessionDuration": 30000
  }
}
```

**Response:**
```json
{
  "analysis": "The user is experiencing a highly positive emotional state with strong joy (0.8) and excellent attention levels (0.9). The high arousal and positive valence suggest active engagement and enthusiasm.",
  "emotionData": {...},
  "context": {...},
  "timestamp": "2025-06-04T12:35:47.342Z",
  "insights": [
    "High positive emotional state detected",
    "Excellent attention levels observed"
  ]
}
```

### Analyze Behavior
Send behavioral data for AI analysis.

**Endpoint:** `POST /api/analyze-behavior`

**Request Body:**
```json
{
  "behaviorData": {
    "activityLevel": "high",
    "engagement": 85,
    "patterns": ["frequent_clicks", "sustained_attention"]
  },
  "context": {
    "source": "behavioral_analysis",
    "sessionDuration": 45000
  }
}
```

**Response:**
```json
{
  "analysis": "The user shows high engagement (85%) with sustained attention patterns. Frequent interaction suggests active participation and focus on current tasks.",
  "behaviorData": {...},
  "context": {...},
  "timestamp": "2025-06-04T12:35:47.342Z",
  "insights": [
    "High user engagement detected",
    "Excellent user engagement score"
  ]
}
```

## 📊 System Status Endpoints

### Health Check
Check if the HRA server is running.

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-04T12:35:47.342Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

### LLM Provider Status
Check the status of connected LLM providers.

**Endpoint:** `GET /api/llm/status`

**Response:**
```json
{
  "providers": {
    "LM Studio": {
      "status": "connected",
      "url": "http://localhost:1234",
      "model": "gemma-2-2b-it",
      "lastResponse": "2025-06-04T12:35:47.342Z"
    }
  },
  "fallbackChain": ["LM Studio"],
  "activeProvider": "LM Studio"
}
```

### Dashboard Data
Get comprehensive dashboard statistics.

**Endpoint:** `GET /api/dashboard`

**Response:**
```json
{
  "sessions": {
    "active": 1,
    "total": 15,
    "averageDuration": 1800
  },
  "emotions": {
    "totalCaptured": 150,
    "averageValence": 0.6,
    "dominantEmotion": "joy"
  },
  "behavior": {
    "totalEvents": 500,
    "averageEngagement": 65,
    "activityLevel": "high"
  },
  "ai": {
    "totalRequests": 75,
    "averageResponseTime": 1200,
    "successRate": 0.98
  }
}
```

## 🔄 WebSocket Events

### Connection
Connect to the WebSocket for real-time updates.

```javascript
const ws = new WebSocket('ws://localhost:9876');

ws.onopen = function() {
    // Send handshake
    ws.send(JSON.stringify({
        type: 'handshake',
        source: 'llm_integration',
        version: '1.0.0',
        modules: {
            emotionProcessing: { enabled: true },
            behaviorAnalysis: { enabled: true }
        }
    }));
};
```

### Incoming Messages

#### Handshake Complete
```json
{
  "type": "handshake_complete",
  "sessionId": "837d6538-4ae4-4da3-80fc-2184c1d21ebb",
  "serverCapabilities": {
    "aiProcessing": true,
    "emotionAnalysis": true,
    "behavioralTracking": true,
    "contextMemory": true,
    "realtimeStreaming": true
  }
}
```

#### Real-time Context Update
```json
{
  "type": "realtime_context_update",
  "category": "emotion",
  "data": {
    "current": {
      "joy": 0.8,
      "arousal": 0.7,
      "valence": 0.8,
      "attention": 0.9
    },
    "trends": {
      "direction": "increasing",
      "magnitude": 0.3,
      "confidence": "high"
    }
  },
  "timestamp": "2025-06-04T12:35:47.342Z",
  "userContext": {
    "emotionalProfile": {...},
    "behaviorPatterns": {...},
    "interests": [...],
    "sessionStats": {...}
  }
}
```

#### Behavioral Update
```json
{
  "type": "realtime_context_update",
  "category": "behavior",
  "data": {
    "patterns": {
      "general_behavior": {
        "frequency": 25,
        "lastActivity": "2025-06-04T12:35:47.342Z",
        "trend": "increasing"
      }
    },
    "recentBehavior": [...],
    "activityLevel": "high",
    "engagement": 85
  },
  "timestamp": "2025-06-04T12:35:47.342Z"
}
```

#### Context Summary
```json
{
  "type": "realtime_context_update",
  "category": "summary",
  "data": {
    "session": "837d6538-4ae4-4da3-80fc-2184c1d21ebb",
    "emotionalState": {
      "primary": {...},
      "stability": "stable",
      "intensity": 0.7
    },
    "behaviorSummary": {...},
    "recentActivity": {...},
    "interests": [...],
    "sessionDuration": 45000
  },
  "timestamp": "2025-06-04T12:35:47.342Z"
}
```

## 🔧 Configuration

### Environment Variables
```bash
# HRA Server Configuration
HRA_PORT=9876
HRA_HOST=localhost

# LLM Integration
LLM_CALLBACK_URL=http://your-llm:5000/hra-context
LLM_SECRET_KEY=your-secret-key

# Streaming Configuration
EMOTION_STREAM_INTERVAL=5000    # 5 seconds
BEHAVIOR_STREAM_INTERVAL=15000  # 15 seconds
CONTEXT_STREAM_INTERVAL=10000   # 10 seconds

# AI Provider Configuration
LM_STUDIO_URL=http://localhost:1234
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
```

### Subscription Filters
```json
{
  "callbackUrl": "http://your-llm:5000/hra-context",
  "events": ["emotion", "behavior"],
  "filters": {
    "emotion": {
      "joy_threshold": 0.7,
      "sadness_threshold": 0.6,
      "attention_threshold": 0.3
    },
    "behavior": {
      "engagement_threshold": 50,
      "activity_level": ["medium", "high"]
    }
  },
  "rateLimit": {
    "maxUpdatesPerMinute": 60,
    "batchUpdates": true
  }
}
```

## ❌ Error Responses

### Standard Error Format
```json
{
  "error": "Invalid request format",
  "code": "INVALID_REQUEST",
  "timestamp": "2025-06-04T12:35:47.342Z",
  "details": {
    "field": "callbackUrl",
    "message": "URL must be a valid HTTP/HTTPS endpoint"
  }
}
```

### Common Error Codes
- `INVALID_REQUEST` - Malformed request
- `UNAUTHORIZED` - Authentication failed
- `RATE_LIMITED` - Too many requests
- `SERVICE_UNAVAILABLE` - HRA server unavailable
- `LLM_PROVIDER_ERROR` - LLM provider connection failed
- `SUBSCRIPTION_FAILED` - Failed to create subscription
- `CONTEXT_UNAVAILABLE` - Context data not available

## 🔒 Authentication

### API Key Authentication
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     http://localhost:9876/api/stream/context
```

### Webhook Signature Verification
```python
import hashlib
import hmac

def verify_webhook_signature(payload, signature, secret):
    expected_signature = hmac.new(
        secret.encode(),
        payload.encode(),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)
```

## 📈 Rate Limits

### Default Limits
- **API Endpoints**: 1000 requests/hour, 100 requests/minute
- **WebSocket Messages**: 60 messages/minute
- **Webhook Callbacks**: 120 calls/minute

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## 🎯 Best Practices

### 1. Handle Webhook Failures Gracefully
```python
@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    try:
        data = request.json
        process_context_update(data)
        return jsonify({"status": "success"})
    except Exception as e:
        logger.error(f"Failed to process HRA update: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500
```

### 2. Implement Exponential Backoff
```python
import time
import random

def subscribe_with_retry(max_retries=5):
    for attempt in range(max_retries):
        try:
            return subscribe_to_hra()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(wait_time)
```

### 3. Cache Context Data
```python
from functools import lru_cache
import time

@lru_cache(maxsize=1)
def get_cached_context():
    return requests.get(f"{HRA_BASE_URL}/api/stream/context").json()

# Clear cache every 30 seconds
def clear_context_cache():
    get_cached_context.cache_clear()
```

---

*For more detailed examples and integration patterns, see the [LLM Integration Guide](./LLM_INTEGRATION_GUIDE.md).*
