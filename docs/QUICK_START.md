# HRA LLM Integration - Quick Start Guide

## 🚀 5-Minute Setup

Get your LLM connected to HRA's real-time streaming system in just 5 minutes!

### Step 1: Verify HRA is Running

```bash
# Check if HRA server is running
curl http://localhost:9876/health

# Expected response:
# {"status":"healthy","timestamp":"2025-06-04T12:35:47.342Z","uptime":3600,"version":"1.0.0"}
```

### Step 2: Subscribe to Real-time Updates

```bash
# Subscribe your LLM to receive context updates
curl -X POST http://localhost:9876/api/llm/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "callbackUrl": "http://your-llm-server:5000/hra-context",
    "events": ["emotion", "behavior", "summary"]
  }'

# Expected response:
# {"subscriberId":"**********","message":"Subscribed to real-time updates"}
```

### Step 3: Create Webhook Endpoint

Create an endpoint to receive HRA updates:

**Python (Flask):**
```python
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    update = request.json
    print(f"Received HRA update: {update['category']}")
    
    # Process the update
    if update['category'] == 'emotion':
        handle_emotion_update(update['data'])
    elif update['category'] == 'behavior':
        handle_behavior_update(update['data'])
    
    return jsonify({"status": "received"})

def handle_emotion_update(emotion_data):
    current = emotion_data.get('current', {})
    if current.get('joy', 0) > 0.7:
        print("User is feeling happy! 😊")
    elif current.get('sadness', 0) > 0.6:
        print("User might need support 🤗")

def handle_behavior_update(behavior_data):
    engagement = behavior_data.get('engagement', 0)
    if engagement > 80:
        print("High user engagement! 🔥")
    elif engagement < 30:
        print("User seems distracted 😴")

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

**Node.js (Express):**
```javascript
const express = require('express');
const app = express();

app.use(express.json());

app.post('/hra-context', (req, res) => {
    const update = req.body;
    console.log(`Received HRA update: ${update.category}`);
    
    if (update.category === 'emotion') {
        handleEmotionUpdate(update.data);
    } else if (update.category === 'behavior') {
        handleBehaviorUpdate(update.data);
    }
    
    res.json({ status: 'received' });
});

function handleEmotionUpdate(emotionData) {
    const current = emotionData.current || {};
    if (current.joy > 0.7) {
        console.log('User is feeling happy! 😊');
    } else if (current.sadness > 0.6) {
        console.log('User might need support 🤗');
    }
}

function handleBehaviorUpdate(behaviorData) {
    const engagement = behaviorData.engagement || 0;
    if (engagement > 80) {
        console.log('High user engagement! 🔥');
    } else if (engagement < 30) {
        console.log('User seems distracted 😴');
    }
}

app.listen(5000, () => {
    console.log('LLM server listening on port 5000');
});
```

### Step 4: Test the Integration

```bash
# Start your LLM server
python app.py  # or node app.js

# In another terminal, check current context
curl http://localhost:9876/api/stream/context

# You should start receiving real-time updates in your LLM server logs!
```

## 🎯 Common Integration Patterns

### Pattern 1: Emotion-Aware Responses

```python
def generate_emotion_aware_response(user_message, emotion_data):
    current = emotion_data.get('current', {})
    
    # Adapt response based on emotional state
    if current.get('joy', 0) > 0.7:
        tone = "enthusiastic and positive"
    elif current.get('sadness', 0) > 0.6:
        tone = "supportive and empathetic"
    elif current.get('attention', 0) < 0.3:
        tone = "brief and engaging"
    else:
        tone = "balanced and helpful"
    
    prompt = f"""
    User message: {user_message}
    Emotional state: {current}
    Response tone: {tone}
    
    Generate an appropriate response considering the user's emotional state.
    """
    
    return call_your_llm(prompt)
```

### Pattern 2: Behavior-Adaptive Interaction

```python
def adapt_interaction_style(behavior_data):
    engagement = behavior_data.get('engagement', 0)
    activity = behavior_data.get('activity', {})
    
    if engagement > 80:
        return {
            'response_length': 'detailed',
            'complexity': 'high',
            'examples': 'multiple'
        }
    elif engagement < 30:
        return {
            'response_length': 'brief',
            'complexity': 'simple',
            'examples': 'one'
        }
    else:
        return {
            'response_length': 'moderate',
            'complexity': 'medium',
            'examples': 'few'
        }
```

### Pattern 3: Context-Aware Chat

```python
import requests

class ContextAwareLLM:
    def __init__(self):
        self.hra_url = "http://localhost:9876"
        self.current_context = {}
    
    def get_current_context(self):
        try:
            response = requests.get(f"{self.hra_url}/api/stream/context")
            return response.json()
        except:
            return {}
    
    def chat(self, user_message):
        # Get latest context
        context = self.get_current_context()
        
        # Build context-aware prompt
        prompt = f"""
        User: {user_message}
        
        Current Context:
        - Emotional State: {context.get('emotionalProfile', {})}
        - Behavior Patterns: {context.get('behaviorPatterns', {})}
        - Engagement: {context.get('sessionStats', {}).get('engagement', 'unknown')}
        
        Respond appropriately considering the user's current state.
        """
        
        return self.call_llm(prompt)
    
    def call_llm(self, prompt):
        # Implement your LLM call here
        return "Context-aware response"

# Usage
llm = ContextAwareLLM()
response = llm.chat("How can I be more productive?")
```

## 🔧 Configuration Examples

### Basic Configuration

```python
# config.py
HRA_BASE_URL = "http://localhost:9876"
LLM_CALLBACK_URL = "http://your-server:5000/hra-context"

SUBSCRIPTION_CONFIG = {
    "callbackUrl": LLM_CALLBACK_URL,
    "events": ["emotion", "behavior", "summary"]
}
```

### Advanced Configuration with Filters

```python
ADVANCED_SUBSCRIPTION = {
    "callbackUrl": LLM_CALLBACK_URL,
    "events": ["emotion", "behavior"],
    "filters": {
        "emotion": {
            "joy_threshold": 0.7,      # Only notify when joy > 0.7
            "sadness_threshold": 0.6,   # Only notify when sadness > 0.6
            "attention_threshold": 0.3  # Only notify when attention < 0.3
        },
        "behavior": {
            "engagement_threshold": 50,  # Only notify when engagement changes by 50+
            "activity_level": ["high"]   # Only notify for high activity
        }
    }
}
```

### Docker Configuration

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

ENV HRA_BASE_URL=http://hra-server:9876
ENV LLM_CALLBACK_URL=http://llm-service:5000/hra-context

EXPOSE 5000
CMD ["python", "app.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  hra-server:
    image: hra:latest
    ports:
      - "9876:9876"
    environment:
      - NODE_ENV=production
  
  llm-service:
    build: .
    ports:
      - "5000:5000"
    environment:
      - HRA_BASE_URL=http://hra-server:9876
      - LLM_CALLBACK_URL=http://llm-service:5000/hra-context
    depends_on:
      - hra-server
```

## 🧪 Testing Your Integration

### Test Script

```python
# test_integration.py
import requests
import time

def test_hra_connection():
    """Test basic HRA connectivity"""
    try:
        response = requests.get("http://localhost:9876/health")
        assert response.status_code == 200
        print("✅ HRA server is running")
        return True
    except:
        print("❌ HRA server is not accessible")
        return False

def test_subscription():
    """Test LLM subscription"""
    try:
        response = requests.post(
            "http://localhost:9876/api/llm/subscribe",
            json={
                "callbackUrl": "http://localhost:5000/hra-context",
                "events": ["emotion", "behavior"]
            }
        )
        assert response.status_code == 200
        print("✅ Successfully subscribed to HRA updates")
        return True
    except:
        print("❌ Failed to subscribe to HRA updates")
        return False

def test_context_fetch():
    """Test context fetching"""
    try:
        response = requests.get("http://localhost:9876/api/stream/context")
        assert response.status_code == 200
        context = response.json()
        print(f"✅ Context fetched: {len(context)} fields")
        return True
    except:
        print("❌ Failed to fetch context")
        return False

def run_tests():
    """Run all integration tests"""
    print("🧪 Testing HRA LLM Integration...")
    
    tests = [
        test_hra_connection,
        test_subscription,
        test_context_fetch
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)
    
    print(f"\n📊 Test Results: {passed}/{len(tests)} passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Your integration is ready.")
    else:
        print("⚠️ Some tests failed. Check your configuration.")

if __name__ == "__main__":
    run_tests()
```

### Manual Testing

```bash
# 1. Check HRA health
curl http://localhost:9876/health

# 2. Subscribe to updates
curl -X POST http://localhost:9876/api/llm/subscribe \
  -H "Content-Type: application/json" \
  -d '{"callbackUrl":"http://localhost:5000/hra-context","events":["all"]}'

# 3. Fetch current context
curl http://localhost:9876/api/stream/context

# 4. Test emotion endpoint
curl http://localhost:9876/api/stream/emotions

# 5. Test behavior endpoint
curl http://localhost:9876/api/stream/behavior
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Connection Refused
```bash
# Check if HRA server is running
curl http://localhost:9876/health

# If not running, start HRA server
cd /path/to/hra
node server.js
```

#### 2. Webhook Not Receiving Updates
```python
# Check if your webhook endpoint is accessible
curl -X POST http://your-server:5000/hra-context \
  -H "Content-Type: application/json" \
  -d '{"test": "message"}'

# Verify subscription
curl http://localhost:9876/api/llm/status
```

#### 3. No Context Data
```bash
# Check if Chrome extension is connected
curl http://localhost:9876/api/dashboard

# Verify WebSocket connections
curl http://localhost:9876/api/stream/context
```

### Debug Mode

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Add debug prints to your webhook
@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    update = request.json
    print(f"DEBUG: Received update: {json.dumps(update, indent=2)}")
    # ... rest of your code
```

## 📚 Next Steps

### 1. Explore Advanced Features
- [Memory Integration with Mem0](./MEMORY_INTEGRATION.md)
- [Multi-LLM Coordination](./MULTI_LLM_GUIDE.md)
- [Custom Emotion Models](./CUSTOM_MODELS.md)

### 2. Production Deployment
- [Kubernetes Deployment](./KUBERNETES_GUIDE.md)
- [Security Best Practices](./SECURITY_GUIDE.md)
- [Monitoring & Analytics](./MONITORING_GUIDE.md)

### 3. Community Resources
- **GitHub**: [HRA Integration Examples](https://github.com/hra-project/examples)
- **Discord**: [Developer Community](https://discord.gg/hra-dev)
- **Documentation**: [Full API Reference](./API_REFERENCE.md)

## 🎯 Example Use Cases

### 1. Therapeutic AI Assistant
```python
def therapeutic_response(user_message, emotion_data):
    if emotion_data.get('sadness', 0) > 0.7:
        return generate_supportive_response(user_message)
    elif emotion_data.get('anxiety', 0) > 0.6:
        return generate_calming_response(user_message)
    else:
        return generate_standard_response(user_message)
```

### 2. Productivity Coach
```python
def productivity_coaching(behavior_data):
    engagement = behavior_data.get('engagement', 0)
    if engagement < 30:
        return "I notice your engagement is low. Would you like to take a break or try a different approach?"
    elif engagement > 90:
        return "You're in the zone! This is a great time to tackle challenging tasks."
```

### 3. Learning Assistant
```python
def adaptive_learning(emotion_data, behavior_data):
    attention = emotion_data.get('attention', 0)
    if attention < 0.4:
        return "Let's try a more interactive approach to keep your attention."
    elif behavior_data.get('engagement', 0) > 80:
        return "You're doing great! Ready for more advanced concepts?"
```

---

**🎉 Congratulations!** You now have a real-time, context-aware LLM integration with HRA. Your AI can understand and respond to user emotions and behavior patterns in real-time!

For more advanced features and examples, check out the [complete documentation](./LLM_INTEGRATION_GUIDE.md).
