# 📁 HRA Project Structure

## 🏗️ Directory Overview

```
HRA-Chrome-Extension/
├── 📄 README.md                    # Main project documentation
├── 📄 package.json                 # Project dependencies and scripts
├── 📄 LICENSE                      # MIT license
├── 📄 .gitignore                   # Git ignore rules
│
├── 📁 src/                         # Chrome Extension Source
│   ├── 📄 manifest.json            # Extension manifest
│   ├── 📄 popup.html               # Extension popup interface
│   ├── 📄 popup.js                 # Popup functionality
│   ├── 📄 background.js             # Background service worker
│   ├── 📄 content.js                # Content script for web pages
│   └── 📁 assets/                   # Extension assets
│       ├── 🖼️ icon16.png           # Extension icons
│       ├── 🖼️ icon48.png
│       └── 🖼️ icon128.png
│
├── 📁 dist/                        # Built Extension (for Chrome)
│   ├── 📄 manifest.json            # Built manifest
│   ├── 📄 popup.html               # Built popup
│   ├── 📄 popup.js                 # Built popup script
│   ├── 📄 background.js             # Built background script
│   ├── 📄 content.js                # Built content script
│   └── 📁 assets/                   # Built assets
│
├── 📁 local-ai-server/             # HRA Local AI Server
│   ├── 📄 server.js                # Main server application
│   ├── 📄 package.json             # Server dependencies
│   ├── 📄 package-lock.json        # Dependency lock file
│   ├── 📄 chat.html                # Chat interface
│   ├── 📄 dashboard.html           # System dashboard
│   ├── 📄 morphcast-test.html      # MorphCast testing interface
│   └── 📁 node_modules/            # Server dependencies
│
├── 📁 docs/                        # Documentation
│   ├── 📄 QUICK_START.md           # 5-minute setup guide
│   ├── 📄 API_REFERENCE.md         # Complete API documentation
│   ├── 📄 LLM_INTEGRATION_GUIDE.md # LLM integration examples
│   ├── 📄 PROJECT_STRUCTURE.md     # This file
│   ├── 📄 USER_GUIDE.md            # User documentation
│   ├── 📄 PRIVACY.md               # Privacy policy
│   ├── 📄 FAQ.md                   # Frequently asked questions
│   ├── 📄 CONTRIBUTING.md          # Contribution guidelines
│   └── 📄 CHANGELOG.md             # Version history
│
├── 📁 examples/                    # Integration Examples
│   ├── 📄 simple_llm_integration.py # Python Flask example
│   ├── 📄 nodejs_integration.js     # Node.js Express example
│   ├── 📄 openai_integration.py     # OpenAI integration
│   ├── 📄 anthropic_integration.py  # Anthropic Claude integration
│   ├── 📄 mem0_integration.py       # Mem0 memory integration
│   └── 📁 docker/                   # Docker examples
│       ├── 📄 Dockerfile            # Docker container
│       ├── 📄 docker-compose.yml    # Multi-service setup
│       └── 📄 kubernetes.yaml       # Kubernetes deployment
│
├── 📁 tests/                       # Test Suite
│   ├── 📄 test_server.js           # Server tests
│   ├── 📄 test_extension.js        # Extension tests
│   ├── 📄 test_integration.py      # Integration tests
│   └── 📁 fixtures/                # Test data
│
└── 📁 scripts/                     # Build and Utility Scripts
    ├── 📄 build.js                 # Build extension
    ├── 📄 deploy.sh                # Deployment script
    ├── 📄 test.sh                  # Run all tests
    └── 📄 setup.sh                 # Initial setup
```

## 🔧 Core Components

### 1. Chrome Extension (`src/`)

#### **manifest.json**
- Extension configuration and permissions
- Defines popup, background, and content scripts
- Specifies required permissions for camera, storage, etc.

#### **popup.html & popup.js**
- Extension popup interface
- Toggle controls for emotion detection, text capture, behavioral tracking
- Connection status and settings

#### **background.js**
- Service worker for extension lifecycle management
- WebSocket connection to HRA server
- Data collection and transmission coordination

#### **content.js**
- Injected into web pages for data collection
- Behavioral tracking (clicks, scrolls, text selections)
- MorphCast emotion detection integration
- Real-time data streaming to background script

### 2. HRA Local AI Server (`local-ai-server/`)

#### **server.js**
- Main server application with Express.js
- WebSocket server for real-time communication
- Multi-LLM manager with fallback chain
- Real-time streaming architecture
- API endpoints for LLM integration

#### **Web Interfaces**
- **chat.html**: Beautiful chat interface with AI
- **dashboard.html**: System monitoring and analytics
- **morphcast-test.html**: Comprehensive emotion testing

### 3. Documentation (`docs/`)

#### **For Developers**
- **QUICK_START.md**: 5-minute setup guide
- **API_REFERENCE.md**: Complete API documentation
- **LLM_INTEGRATION_GUIDE.md**: Comprehensive integration examples

#### **For Users**
- **USER_GUIDE.md**: How to use the HRA system
- **PRIVACY.md**: Data handling and privacy information
- **FAQ.md**: Frequently asked questions

### 4. Integration Examples (`examples/`)

#### **Language Examples**
- **Python Flask**: Complete integration with emotion/behavior handling
- **Node.js Express**: Real-time WebSocket integration
- **OpenAI Integration**: GPT model integration
- **Anthropic Integration**: Claude model integration

#### **Deployment Examples**
- **Docker**: Containerized deployment
- **Kubernetes**: Scalable cloud deployment
- **Docker Compose**: Multi-service local development

## 📊 Data Flow Architecture

```
┌─────────────────┐    WebSocket     ┌──────────────────┐    HTTP/WS    ┌─────────────────┐
│  Chrome         │◄─────────────────►│  HRA Local       │◄──────────────►│  LLM            │
│  Extension      │                  │  AI Server       │               │  Integration    │
│                 │                  │                  │               │                 │
│ ┌─────────────┐ │                  │ ┌──────────────┐ │               │ ┌─────────────┐ │
│ │ content.js  │ │                  │ │ server.js    │ │               │ │ Your LLM    │ │
│ │             │ │                  │ │              │ │               │ │ Service     │ │
│ │ • Emotion   │ │                  │ │ • Streaming  │ │               │ │             │ │
│ │ • Behavior  │ │                  │ │ • Multi-LLM  │ │               │ │ • Context   │ │
│ │ • Text      │ │                  │ │ • Memory     │ │               │ │ • Responses │ │
│ │ • Activity  │ │                  │ │ • Analytics  │ │               │ │ • Learning  │ │
│ └─────────────┘ │                  │ └──────────────┘ │               │ └─────────────┘ │
│                 │                  │                  │               │                 │
│ ┌─────────────┐ │                  │ ┌──────────────┐ │               │ ┌─────────────┐ │
│ │ background  │ │                  │ │ Web          │ │               │ │ Webhook     │ │
│ │             │ │                  │ │ Interfaces   │ │               │ │ Endpoint    │ │
│ │ • WebSocket │ │                  │ │              │ │               │ │             │ │
│ │ • Coord.    │ │                  │ │ • Dashboard  │ │               │ │ • Updates   │ │
│ │ • Storage   │ │                  │ │ • Chat       │ │               │ │ • Context   │ │
│ │ • Sync      │ │                  │ │ • MorphCast  │ │               │ │ • Memory    │ │
│ └─────────────┘ │                  │ └──────────────┘ │               │ └─────────────┘ │
└─────────────────┘                  └──────────────────┘               └─────────────────┘
         │                                     │                                 │
         │                            ┌──────────────────┐                      │
         └────────────────────────────►│  Mem0 Memory     │◄─────────────────────┘
                                      │  Integration     │
                                      │                  │
                                      │ • Long-term      │
                                      │   Learning       │
                                      │ • Pattern        │
                                      │   Recognition    │
                                      │ • User Profiles  │
                                      └──────────────────┘
```

## 🔄 Real-time Streaming Flow

### 1. Data Collection (Chrome Extension)
```
content.js → background.js → WebSocket → HRA Server
```

### 2. Processing (HRA Server)
```
WebSocket Handler → Data Processing → Pattern Analysis → Context Building
```

### 3. Distribution (Real-time Streaming)
```
Context Updates → LLM Subscribers → Webhook Callbacks → AI Processing
```

### 4. Response (AI Integration)
```
LLM Response → Context Adaptation → User Interface → Feedback Loop
```

## 🛠️ Development Workflow

### 1. Setup Development Environment
```bash
# Clone repository
git clone https://github.com/your-org/hra-chrome-extension.git
cd hra-chrome-extension

# Install server dependencies
cd local-ai-server
npm install

# Start development server
npm run dev
```

### 2. Extension Development
```bash
# Build extension for development
npm run build:dev

# Load extension in Chrome
# 1. Go to chrome://extensions/
# 2. Enable Developer mode
# 3. Click "Load unpacked"
# 4. Select the dist/ folder
```

### 3. Testing
```bash
# Run server tests
npm test

# Run integration tests
python tests/test_integration.py

# Manual testing
curl http://localhost:9876/health
```

### 4. Building for Production
```bash
# Build optimized extension
npm run build:prod

# Package for distribution
npm run package

# Deploy server
npm run deploy
```

## 📦 Dependencies

### Chrome Extension
- **Manifest V3** - Modern extension architecture
- **WebSocket API** - Real-time communication
- **MorphCast SDK** - Emotion detection
- **Chrome APIs** - Storage, tabs, permissions

### HRA Server
- **Express.js** - Web server framework
- **ws** - WebSocket server implementation
- **node-fetch** - HTTP client for LLM APIs
- **cors** - Cross-origin resource sharing

### LLM Integration
- **Flask/Express** - Web framework for webhooks
- **Requests/Axios** - HTTP client libraries
- **OpenAI SDK** - GPT integration
- **Anthropic SDK** - Claude integration

## 🔒 Security Considerations

### Data Protection
- **Local Processing** - Emotion detection happens locally
- **Encrypted Communication** - All data transmission encrypted
- **No Persistent Storage** - Real-time processing only
- **User Consent** - Explicit permission for all data collection

### API Security
- **Authentication** - API keys and tokens
- **Rate Limiting** - Protection against abuse
- **CORS** - Controlled cross-origin access
- **Input Validation** - Sanitized user inputs

## 📈 Monitoring & Logging

### Server Monitoring
- **Health Checks** - `/health` endpoint
- **Performance Metrics** - Response times, memory usage
- **Error Logging** - Comprehensive error tracking
- **Real-time Stats** - Active sessions, data throughput

### Extension Monitoring
- **Connection Status** - WebSocket connectivity
- **Data Collection** - Successful data transmission
- **Error Reporting** - Client-side error tracking
- **User Engagement** - Feature usage analytics

## 🚀 Deployment Options

### Local Development
- **Node.js Server** - Direct execution
- **Chrome Extension** - Developer mode loading
- **LM Studio** - Local LLM hosting

### Production Deployment
- **Docker Containers** - Containerized services
- **Kubernetes** - Scalable cloud deployment
- **Chrome Web Store** - Extension distribution
- **Cloud Hosting** - AWS, GCP, Azure deployment

---

This structure provides a comprehensive, scalable foundation for building emotion-aware AI systems with real-time streaming capabilities. Each component is designed to be modular, testable, and easily extensible.
