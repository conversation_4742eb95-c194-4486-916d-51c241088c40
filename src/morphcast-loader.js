// MorphCast SDK Loader for HRA Chrome Extension
// This script loads the MorphCast SDK and initializes the emotion detection system

(function() {
    'use strict';
    
    // Check if MorphCast is already loaded
    if (window.MorphCastLoader) {
        return;
    }
    
    window.MorphCastLoader = {
        isLoaded: false,
        isLoading: false,
        callbacks: [],
        
        // Load MorphCast SDK
        load: function(callback) {
            if (this.isLoaded) {
                if (callback) callback();
                return;
            }
            
            if (callback) {
                this.callbacks.push(callback);
            }
            
            if (this.isLoading) {
                return;
            }
            
            this.isLoading = true;
            
            // Create script element
            const script = document.createElement('script');
            script.src = 'https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js';
            script.async = true;
            
            script.onload = () => {
                console.log('MorphCast SDK loaded successfully');
                this.isLoaded = true;
                this.isLoading = false;
                
                // Execute all callbacks
                this.callbacks.forEach(cb => {
                    try {
                        cb();
                    } catch (error) {
                        console.error('MorphCast callback error:', error);
                    }
                });
                this.callbacks = [];
            };
            
            script.onerror = () => {
                console.error('Failed to load MorphCast SDK');
                this.isLoading = false;
                
                // Execute callbacks with error
                this.callbacks.forEach(cb => {
                    try {
                        cb(new Error('Failed to load MorphCast SDK'));
                    } catch (error) {
                        console.error('MorphCast error callback error:', error);
                    }
                });
                this.callbacks = [];
            };
            
            // Add to head
            document.head.appendChild(script);
        },
        
        // Check if SDK is available
        isAvailable: function() {
            return this.isLoaded && window.MorphCast;
        }
    };
    
    // Auto-load if not in extension context
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        window.MorphCastLoader.load();
    }
    
})();
