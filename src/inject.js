// HRA (Harmonic Resonance Agent) - Modern MorphCast Integration Injection Script
// Updated to use CY.loader() API and ai-sdk.js v1.16

class HRAMorphCastModule {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        this.cyInstance = null;
        this.startFunction = null;
        this.stopFunction = null;
        
        // License configuration
        this.config = {
            licenseKey: 'sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b',
            email: '<EMAIL>'
        };
        
        // Emotion tracking state
        this.emotionHistory = [];
        this.currentEmotionState = null;
        this.sessionStartTime = Date.now();
        this.lastEmotionUpdate = 0;
        
        // Auto-stop configuration (15 minutes)
        this.autoStopTimeout = 15 * 60 * 1000;
        this.autoStopTimer = null;
        
        this.initialize();
    }

    async initialize() {
        try {
            // Listen for messages from content script
            window.addEventListener('message', (event) => {
                if (event.source === window && event.data) {
                    this.handleMessage(event.data);
                }
            });

            // Load modern MorphCast SDK
            await this.loadMorphCastSDK();
            console.log('HRA MorphCast Module initialized successfully');
            this.isInitialized = true;

        } catch (error) {
            console.error('Failed to initialize HRA MorphCast Module:', error);
            this.isInitialized = false;
            
            this.sendMessage({
                type: 'MORPHCAST_UNAVAILABLE',
                error: error.message
            });
        }
    }

    async loadMorphCastSDK() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (window.CY && window.MphTools) {
                resolve();
                return;
            }

            let scriptsLoaded = 0;
            const totalScripts = 3;
            const scriptErrors = [];

            const checkComplete = () => {
                if (scriptsLoaded === totalScripts) {
                    if (window.CY && window.MphTools) {
                        console.log('Modern MorphCast SDK loaded successfully');
                        resolve();
                    } else {
                        reject(new Error('Scripts loaded but CY/MphTools not available'));
                    }
                } else if (scriptErrors.length > 0) {
                    reject(new Error(`Failed to load scripts: ${scriptErrors.join(', ')}`));
                }
            };

            const loadScript = (src, name) => {
                const script = document.createElement('script');
                script.src = src;
                script.async = true;
                script.crossOrigin = 'anonymous';

                script.onload = () => {
                    console.log(`${name} loaded successfully`);
                    scriptsLoaded++;
                    checkComplete();
                };

                script.onerror = () => {
                    console.error(`Failed to load ${name}`);
                    scriptErrors.push(name);
                    checkComplete();
                };

                document.head.appendChild(script);
            };

            // Load required scripts
            loadScript('https://sdk.morphcast.com/mphtools/v1.1/mphtools.js', 'MphTools');
            loadScript('https://ai-sdk.morphcast.com/v1.16/ai-sdk.js', 'AI-SDK');
            loadScript('https://sdk.morphcast.com/emotion-statistics/v1.0-beta/script.js', 'Statistics');
        });
    }

    handleMessage(data) {
        switch (data.type) {
            case 'START_MORPHCAST':
                this.startEmotionCapture();
                break;
                
            case 'STOP_MORPHCAST':
                this.stopEmotionCapture();
                break;
                
            case 'GET_EMOTION_STATE':
                this.sendCurrentEmotionState();
                break;
                
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    async startEmotionCapture() {
        if (this.isRunning || !this.isInitialized) {
            console.warn('MorphCast already running or not initialized');
            return;
        }

        try {
            console.log('Starting HRA emotion capture with modern API...');
            
            // Set up camera privacy popup
            if (window.MphTools && window.MphTools.CameraPrivacyPopup) {
                window.MphTools.CameraPrivacyPopup.setText({
                    "title": "HRA Camera Access",
                    "description": "Harmonic Resonance Agent needs camera access for emotion detection. Your privacy is protected - data stays local.",
                    "url": "about:blank" // Could link to privacy policy
                });
            }

            // Configure statistics uploader
            const statsConfig = {
                sendDataInterval: 5000,
                tickInterval: 1000,
                stopAfter: this.autoStopTimeout,
                licenseKey: this.config.licenseKey
            };
            
            this.statisticsUploader = new window.MorphCastStatistics.StatisticsUploader(statsConfig);

            // Initialize CY loader with all modules
            const cyLoader = window.CY.loader()
                .licenseKey(this.config.licenseKey)
                .addModule(window.CY.modules().FACE_AROUSAL_VALENCE.name, {smoothness: 0.70})
                .addModule(window.CY.modules().FACE_EMOTION.name, {smoothness: 0.40})
                .addModule(window.CY.modules().FACE_ATTENTION.name, {smoothness: 0.83})
                .addModule(window.CY.modules().ALARM_LOW_ATTENTION.name, {
                    timeWindowMs: 5000, 
                    initialToleranceMs: 7000, 
                    threshold: 0.33
                })
                .addModule(window.CY.modules().FACE_POSE.name, {smoothness: 0.65})
                .addModule(window.CY.modules().FACE_AGE.name, {rawOutput: false})
                .addModule(window.CY.modules().FACE_GENDER.name, {smoothness: 0.95, threshold: 0.70})
                .addModule(window.CY.modules().FACE_FEATURES.name, {smoothness: 0.90})
                .addModule(window.CY.modules().FACE_DETECTOR.name, {maxInputFrameSize: 320, smoothness: 0.83})
                .addModule(window.CY.modules().DATA_AGGREGATOR.name, {initialWaitMs: 2000, periodMs: 1000})
                .addModule(window.CY.modules().FACE_POSITIVITY.name, {smoothness: 0.40, gain: 2, angle: 17});

            // Load and start
            const { start, stop } = await cyLoader.load();
            this.startFunction = start;
            this.stopFunction = stop;

            // Set up event listeners for all modules
            this.setupEventListeners();

            // Start emotion capture
            await this.startFunction();
            await this.statisticsUploader.start();
            
            this.isRunning = true;
            this.sessionStartTime = Date.now();
            
            // Set up auto-stop timer
            this.autoStopTimer = setTimeout(async () => {
                console.log('Auto-stopping MorphCast after 15 minutes');
                await this.stopEmotionCapture();
            }, this.autoStopTimeout);
            
            console.log('HRA emotion capture started successfully');
            
            // Notify content script
            this.sendMessage({ type: 'MORPHCAST_STARTED' });
            
        } catch (error) {
            console.error('Failed to start emotion capture:', error);
            this.cleanup();
            
            this.sendMessage({
                type: 'MORPHCAST_ERROR',
                error: error.message
            });
        }
    }

    setupEventListeners() {
        // Set up listeners for all emotion detection modules
        const modules = [
            'FACE_AROUSAL_VALENCE',
            'FACE_EMOTION', 
            'FACE_ATTENTION',
            'ALARM_LOW_ATTENTION',
            'FACE_POSE',
            'FACE_AGE',
            'FACE_GENDER',
            'FACE_FEATURES',
            'FACE_DETECTOR',
            'DATA_AGGREGATOR',
            'FACE_POSITIVITY'
        ];

        modules.forEach(moduleName => {
            if (window.CY.modules()[moduleName]) {
                window.addEventListener(window.CY.modules()[moduleName].eventName, (evt) => {
                    this.handleModuleEvent(moduleName, evt.detail);
                });
            }
        });
    }

    handleModuleEvent(moduleName, data) {
        const currentTime = Date.now();
        
        // Create comprehensive emotion data object
        const emotionData = {
            timestamp: currentTime,
            sessionTime: currentTime - this.sessionStartTime,
            module: moduleName,
            data: data
        };

        // Process specific module types
        switch (moduleName) {
            case 'FACE_EMOTION':
                this.processEmotionData(emotionData);
                break;
            case 'FACE_AROUSAL_VALENCE':
                this.processArousalValenceData(emotionData);
                break;
            case 'FACE_ATTENTION':
                this.processAttentionData(emotionData);
                break;
            case 'DATA_AGGREGATOR':
                this.processAggregatedData(emotionData);
                break;
            default:
                // Store all data for comprehensive tracking
                this.storeEmotionData(emotionData);
        }

        // Send to content script (throttled)
        if (currentTime - this.lastEmotionUpdate > 200) {
            this.sendEmotionUpdate(emotionData);
            this.lastEmotionUpdate = currentTime;
        }
    }

    processEmotionData(emotionData) {
        if (!emotionData.data) return;
        
        // Extract dominant emotion
        const emotions = emotionData.data;
        let dominantEmotion = 'neutral';
        let maxValue = 0;

        Object.keys(emotions).forEach(emotion => {
            if (emotions[emotion] > maxValue) {
                maxValue = emotions[emotion];
                dominantEmotion = emotion;
            }
        });

        emotionData.dominantEmotion = dominantEmotion;
        emotionData.emotionStrength = maxValue;
        
        this.storeEmotionData(emotionData);
    }

    processArousalValenceData(emotionData) {
        if (!emotionData.data) return;
        
        emotionData.arousal = emotionData.data.arousal;
        emotionData.valence = emotionData.data.valence;
        emotionData.quadrant = this.determineQuadrant(emotionData.arousal, emotionData.valence);
        
        this.storeEmotionData(emotionData);
    }

    processAttentionData(emotionData) {
        if (!emotionData.data) return;
        
        emotionData.attention = emotionData.data.attention;
        emotionData.engagementLevel = this.classifyEngagementLevel(emotionData.attention);
        
        this.storeEmotionData(emotionData);
    }

    processAggregatedData(emotionData) {
        // This is comprehensive data from multiple modules
        this.currentEmotionState = emotionData;
        this.storeEmotionData(emotionData);
    }

    storeEmotionData(emotionData) {
        this.emotionHistory.push(emotionData);
        
        // Keep history manageable (last 100 entries)
        if (this.emotionHistory.length > 100) {
            this.emotionHistory = this.emotionHistory.slice(-100);
        }
    }

    determineQuadrant(arousal, valence) {
        if (arousal > 0.2 && valence > 0.2) return 'high_positive';
        if (arousal > 0.2 && valence < -0.2) return 'high_negative';
        if (arousal < -0.2 && valence > 0.2) return 'low_positive';
        if (arousal < -0.2 && valence < -0.2) return 'low_negative';
        return 'neutral';
    }

    classifyEngagementLevel(attention) {
        if (attention > 0.8) return 'highly_engaged';
        if (attention > 0.6) return 'engaged';
        if (attention > 0.4) return 'moderately_engaged';
        if (attention > 0.2) return 'low_engagement';
        return 'disengaged';
    }

    sendEmotionUpdate(emotionData) {
        this.sendMessage({
            type: 'EMOTION_UPDATE',
            data: emotionData
        });
    }

    sendCurrentEmotionState() {
        if (this.currentEmotionState) {
            this.sendMessage({
                type: 'CURRENT_EMOTION_STATE',
                data: this.currentEmotionState
            });
        }
    }

    sendMessage(message) {
        window.postMessage(message, '*');
    }

    async stopEmotionCapture() {
        console.log('Stopping HRA emotion capture');
        
        this.isRunning = false;
        
        try {
            // Stop statistics uploader
            if (this.statisticsUploader) {
                await this.statisticsUploader.stop();
            }
            
            // Stop MorphCast
            if (this.stopFunction) {
                await this.stopFunction();
            }
            
            // Clear auto-stop timer
            if (this.autoStopTimer) {
                clearTimeout(this.autoStopTimer);
                this.autoStopTimer = null;
            }
            
            // Send final emotion summary
            this.sendEmotionSummary();
            
            // Notify content script
            this.sendMessage({ type: 'MORPHCAST_STOPPED' });
            
        } catch (error) {
            console.error('Error stopping MorphCast:', error);
        }
    }

    sendEmotionSummary() {
        if (this.emotionHistory.length === 0) return;

        const sessionDuration = Date.now() - this.sessionStartTime;
        const summary = {
            sessionDuration,
            totalEvents: this.emotionHistory.length,
            emotionHistory: this.emotionHistory.slice(-20), // Last 20 events
            sessionStartTime: this.sessionStartTime
        };
        
        this.sendMessage({
            type: 'EMOTION_SESSION_SUMMARY',
            data: summary
        });
    }

    cleanup() {
        this.isRunning = false;
        this.startFunction = null;
        this.stopFunction = null;
        this.cyInstance = null;
        
        if (this.autoStopTimer) {
            clearTimeout(this.autoStopTimer);
            this.autoStopTimer = null;
        }
    }
}

// Initialize MorphCast module
let hraMorphCast = null;

// Listen for initialization message
window.addEventListener('message', (event) => {
    if (event.source === window && event.data && event.data.type === 'START_MORPHCAST') {
        if (!hraMorphCast) {
            hraMorphCast = new HRAMorphCastModule();
        }
        
        // Delay to ensure initialization
        setTimeout(() => {
            if (hraMorphCast.isInitialized) {
                hraMorphCast.startEmotionCapture();
            }
        }, 1000);
    }
});

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('HRA Modern MorphCast injection script loaded');
});
