// Debug Extension Test
console.log('🔍 HRA Extension Debug Test Started');

// Test 1: Extension Context
console.log('1. Extension Context Test:');
console.log('   chrome.runtime.id:', chrome.runtime?.id);
console.log('   chrome available:', typeof chrome !== 'undefined');

// Test 2: Message System
console.log('2. Testing Background Communication...');
chrome.runtime.sendMessage({ type: 'debug_test' })
    .then(response => {
        console.log('   ✅ Background response:', response);
    })
    .catch(error => {
        console.log('   ❌ Background error:', error.message);
    });

// Test 3: Camera Permission Test
console.log('3. Testing Camera Access...');
navigator.mediaDevices.getUserMedia({ video: true })
    .then(stream => {
        console.log('   ✅ Camera access granted');
        stream.getTracks().forEach(track => track.stop());
    })
    .catch(error => {
        console.log('   ❌ Camera access denied:', error.message);
    });

// Test 4: MorphCast SDK Load Test
console.log('4. Testing MorphCast SDK...');
const script = document.createElement('script');
script.src = 'https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js';
script.onload = () => {
    console.log('   ✅ MorphCast SDK loaded successfully');
    console.log('   MorphCast object:', typeof window.MorphCast);
};
script.onerror = () => {
    console.log('   ❌ MorphCast SDK failed to load');
};
document.head.appendChild(script);

console.log('🔍 Debug tests initiated. Check console for results.');
