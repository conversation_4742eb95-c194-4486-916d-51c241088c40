// Test script for the HRA Chrome Extension
// This simulates the extension context invalidated error and tests our error handling

function isExtensionValid() {
    return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
}

function testExtensionCommunication() {
    console.log("Testing extension communication...");
    
    if (!isExtensionValid()) {
        console.log("PASS: Extension context validation correctly identifies invalid context");
    } else {
        try {
            // Try sending a message to background
            chrome.runtime.sendMessage({
                type: 'testCommunication'
            }, response => {
                console.log("Response from background:", response);
                console.log("PASS: Communication with background is working");
            });
        } catch (error) {
            console.log("Error during communication test:", error.message);
            if (error.message.includes("Extension context invalidated")) {
                console.log("PASS: Error properly caught and identified");
            } else {
                console.log("FAIL: Unexpected error");
            }
        }
    }
}

// Simulates sending data with error handling
function sendWithErrorHandling(data) {
    console.log("Testing send with error handling...");
    
    if (!isExtensionValid()) {
        console.log("PASS: Caught invalid extension context before attempting to send");
        return;
    }
    
    try {
        chrome.runtime.sendMessage({
            type: 'testData',
            data: data
        });
        console.log("PASS: Message sent successfully");
    } catch (error) {
        console.log("Error caught during send:", error.message);
        if (error.message.includes("Extension context invalidated")) {
            console.log("PASS: Extension context error properly caught");
        } else {
            console.log("FAIL: Unexpected error during send");
        }
    }
}

// Run tests
console.log("=== HRA Extension Test Suite ===");
testExtensionCommunication();
sendWithErrorHandling({ test: "data" });
console.log("=== Test Suite Complete ===");
