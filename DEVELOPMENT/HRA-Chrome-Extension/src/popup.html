<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Harmonic Resonance Agent</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #fc8181;
            --bg-color: #fff;
            --bg-secondary: #f7fafc;
            --text-color: #2d3748;
            --text-secondary: #718096;
            --success-color: #48bb78;
            --warning-color: #f6ad55;
            --error-color: #f56565;
            --border-color: #e2e8f0;
            --border-radius: 12px;
            --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.2s ease;
        }

        [data-theme="dark"] {
            --primary-color: #7f9cf5;
            --primary-dark: #667eea;
            --secondary-color: #fc8181;
            --bg-color: #1a202c;
            --bg-secondary: #2d3748;
            --text-color: #f7fafc;
            --text-secondary: #e2e8f0;
            --border-color: #4a5568;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            width: 350px;
            height: 500px;
            background-color: var(--bg-color);
            color: var(--text-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
        }

        header {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border-color);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 28px;
            height: 28px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .theme-toggle, .settings-btn {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .theme-toggle:hover, .settings-btn:hover {
            background-color: var(--bg-secondary);
            color: var(--primary-color);
        }

        .connection-status {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            font-size: 13px;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--bg-secondary);
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background-color: var(--success-color);
        }

        .status-disconnected {
            background-color: var(--error-color);
        }

        .main-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .card {
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
        }

        .module-toggle {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .module-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        .module-description {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .module-status {
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-badge {
            font-size: 11px;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .status-inactive {
            background-color: rgba(237, 137, 54, 0.1);
            color: var(--warning-color);
        }

        .status-error {
            background-color: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
        }

        .emotion-card {
            position: relative;
            overflow: hidden;
        }

        .emotion-data {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 12px;
        }

        .emotion-metric {
            display: flex;
            flex-direction: column;
            padding: 10px;
            background-color: var(--bg-secondary);
            border-radius: 8px;
            flex: 1 0 calc(33.333% - 8px);
            min-width: 90px;
            text-align: center;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .metric-label {
            font-size: 11px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .emotion-preview {
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--error-color), var(--warning-color), var(--success-color));
            border-radius: 2px;
            margin-top: 12px;
        }

        .emotion-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 120px;
            color: var(--text-secondary);
            text-align: center;
            padding: 0 20px;
        }

        .emotion-placeholder svg {
            margin-bottom: 10px;
            opacity: 0.5;
        }

        .emotion-placeholder-text {
            font-size: 14px;
            max-width: 240px;
        }

        .session-stats {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .session-stat {
            flex: 1;
            background-color: var(--bg-secondary);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
        }

        .stat-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .footer {
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-secondary);
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-color);
        }

        .footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .version {
            font-weight: 500;
        }

        /* SVG Icons */
        .icon {
            width: 20px;
            height: 20px;
            stroke-width: 2;
            stroke: currentColor;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        /* Animations */
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                opacity: 0.7;
            }
            70% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(0.95);
                opacity: 0.7;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            animation: rotate 1s linear infinite;
        }

        @keyframes rotate {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="logo">
            <img src="icons/icon48.png" alt="HRA Logo" class="logo-icon">
            <div class="logo-text">Harmonic Resonance</div>
        </div>
        <div class="header-actions">
            <button id="themeToggle" class="theme-toggle" title="Toggle dark mode">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
            </button>
            <button id="settingsBtn" class="settings-btn" title="Settings">
                <svg class="icon" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                </svg>
            </button>
        </div>
    </header>

    <div class="connection-status">
        <span id="statusIndicator" class="status-indicator status-disconnected"></span>
        <span id="statusText">Connecting to Local AI Server...</span>
    </div>

    <div class="main-content">
        <div class="card emotion-card">
            <div class="card-header">
                <div class="card-title">Emotion Detection</div>
                <label class="module-toggle">
                    <input type="checkbox" id="emotionToggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="module-description">
                Captures and analyzes emotional states through webcam
            </div>
            <div id="emotionContent" class="emotion-placeholder">
                <svg class="icon" width="40" height="40" viewBox="0 0 24 24">
                    <circle cx="12" cy="8" r="7"></circle>
                    <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                </svg>
                <div class="emotion-placeholder-text">Enable emotion detection to start capturing your emotional resonance</div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title">Text Capture</div>
                <label class="module-toggle">
                    <input type="checkbox" id="textToggle" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="module-description">
                Captures selected text and contextual information
            </div>
            <div class="module-status">
                <span class="status-badge status-active">Active</span>
                <span id="textStatus">Tracking text selections</span>
            </div>
            <div class="session-stats">
                <div class="session-stat">
                    <div id="selectionCount" class="stat-value">0</div>
                    <div class="stat-label">Selections</div>
                </div>
                <div class="session-stat">
                    <div id="copyCount" class="stat-value">0</div>
                    <div class="stat-label">Copies</div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title">Behavioral Tracking</div>
                <label class="module-toggle">
                    <input type="checkbox" id="behavioralToggle" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            <div class="module-description">
                Tracks page engagement and interaction patterns
            </div>
            <div class="module-status">
                <span class="status-badge status-active">Active</span>
                <span id="behavioralStatus">Monitoring engagement</span>
            </div>
            <div class="session-stats">
                <div class="session-stat">
                    <div id="focusPercentage" class="stat-value">100%</div>
                    <div class="stat-label">Focus</div>
                </div>
                <div class="session-stat">
                    <div id="scrollDepth" class="stat-value">0%</div>
                    <div class="stat-label">Scroll</div>
                </div>
                <div class="session-stat">
                    <div id="clickCount" class="stat-value">0</div>
                    <div class="stat-label">Clicks</div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div>
            <span class="version">v1.0.0</span> | <a href="#" id="openDashboard">Open Dashboard</a>
        </div>
        <div>Harmonic Resonance Agent</div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
