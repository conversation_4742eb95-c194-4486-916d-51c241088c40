// HRA Chrome Extension - Background Service Worker
// Manages connection to Local AI Server and coordinates all modules

class HRABackground {
    constructor() {
        this.LAS_URL = 'ws://localhost:9876';
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000;
        
        // Message queues
        this.messageQueue = [];
        this.contextBuffer = [];
        
        // Module states
        this.modules = {
            textCapture: { enabled: true },
            morphcast: { enabled: false },
            behavioral: { enabled: true }
        };
        
        // User preferences
        this.userPreferences = {
            captureMode: 'selective', // 'all' or 'selective'
            emotionCapture: true,
            privacyMode: false
        };
        
        this.initialize();
    }

    async initialize() {
        // Load stored preferences
        const stored = await chrome.storage.local.get(['userPreferences', 'lasConnectionStatus']);
        if (stored.userPreferences) {
            this.userPreferences = { ...this.userPreferences, ...stored.userPreferences };
        }
        
        // Set up message listeners
        this.setupMessageListeners();
        
        // Attempt to connect to LAS
        this.connectToLAS();
        
        // Set up periodic context sync
        this.setupPeriodicSync();
        
        console.log('HRA Background Service initialized');
    }

    connectToLAS() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            return;
        }

        try {
            this.socket = new WebSocket(this.LAS_URL);
            
            this.socket.onopen = () => {
                console.log('Connected to Local AI Server');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                
                // Send authentication/handshake
                this.send({
                    type: 'handshake',
                    source: 'chrome_extension',
                    version: '1.0.0',
                    modules: this.modules
                });
                
                // Process queued messages
                this.processMessageQueue();
                
                // Update connection status
                chrome.storage.local.set({ lasConnectionStatus: 'connected' });
                this.broadcastConnectionStatus('connected');
            };
            
            this.socket.onmessage = (event) => {
                this.handleLASMessage(JSON.parse(event.data));
            };
            
            this.socket.onerror = (error) => {
                console.error('LAS connection error:', error);
            };
            
            this.socket.onclose = () => {
                console.log('Disconnected from Local AI Server');
                this.isConnected = false;
                chrome.storage.local.set({ lasConnectionStatus: 'disconnected' });
                this.broadcastConnectionStatus('disconnected');
                
                // Attempt reconnection
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    setTimeout(() => {
                        this.reconnectAttempts++;
                        this.connectToLAS();
                    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
                }
            };
        } catch (error) {
            console.error('Failed to connect to LAS:', error);
        }
    }

    setupMessageListeners() {
        // Listen for messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            switch (request.type) {
                case 'capturedText':
                    this.handleCapturedText(request.data, sender.tab);
                    break;
                    
                case 'emotionData':
                    this.handleEmotionData(request.data, sender.tab);
                    break;
                    
                case 'behavioralData':
                    this.handleBehavioralData(request.data, sender.tab);
                    break;
                    
                case 'getLASStatus':
                    sendResponse({ connected: this.isConnected });
                    break;

                case 'getSessionStats':
                    this.getSessionStats(sendResponse);
                    return true; // Will respond asynchronously

                case 'updatePreferences':
                    this.updatePreferences(request.preferences);
                    break;

                case 'toggleModule':
                    this.toggleModule(request.module, request.enabled);
                    break;

                case 'requestContext':
                    this.requestContextFromLAS(request.query, sendResponse);
                    return true; // Will respond asynchronously

                case 'testCommunication':
                    this.testCommunication(sendResponse);
                    return true; // Will respond asynchronously

                default:
                    console.warn('Unknown message type:', request.type);
            }
        });

        // Listen for tab events for behavioral tracking
        chrome.tabs.onActivated.addListener((activeInfo) => {
            if (this.modules.behavioral.enabled) {
                this.trackTabActivity('activated', activeInfo);
            }
        });

        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (this.modules.behavioral.enabled && changeInfo.status === 'complete') {
                this.trackTabActivity('navigated', { tabId, url: tab.url, title: tab.title });
            }
        });

        chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
            if (this.modules.behavioral.enabled) {
                this.trackTabActivity('closed', { tabId });
            }
        });
    }

    handleCapturedText(data, tab) {
        const contextEntry = {
            type: 'text_capture',
            timestamp: Date.now(),
            source: {
                tabId: tab.id,
                url: tab.url,
                title: tab.title
            },
            data: {
                text: data.text,
                elementType: data.elementType,
                action: data.action
            }
        };
        
        this.addToContextBuffer(contextEntry);
        
        // Send to LAS if connected
        if (this.isConnected) {
            this.send({
                type: 'context_update',
                entry: contextEntry
            });
        } else {
            this.queueMessage(contextEntry);
        }
    }

    handleEmotionData(data, tab) {
        const emotionEntry = {
            type: 'emotion_capture',
            timestamp: Date.now(),
            source: {
                tabId: tab.id,
                url: tab.url
            },
            data: data
        };
        
        // Always send emotion data immediately if connected
        if (this.isConnected) {
            this.send({
                type: 'emotion_update',
                entry: emotionEntry
            });
        }
        
        // Also add to context buffer for correlation
        this.addToContextBuffer(emotionEntry);
    }

    handleBehavioralData(data, tab) {
        const behavioralEntry = {
            type: 'behavioral_capture',
            timestamp: Date.now(),
            source: {
                tabId: tab.id,
                url: tab.url
            },
            data: data
        };
        
        this.addToContextBuffer(behavioralEntry);
        
        if (this.isConnected) {
            this.send({
                type: 'behavioral_update',
                entry: behavioralEntry
            });
        }
    }

    trackTabActivity(action, details) {
        const activityEntry = {
            type: 'tab_activity',
            timestamp: Date.now(),
            action: action,
            details: details
        };
        
        this.addToContextBuffer(activityEntry);
        
        if (this.isConnected) {
            this.send({
                type: 'activity_update',
                entry: activityEntry
            });
        }
    }

    handleLASMessage(message) {
        switch (message.type) {
            case 'handshake_ack':
                console.log('LAS handshake acknowledged');
                break;
                
            case 'context_response':
                // Forward to appropriate tab or popup
                if (message.requestId) {
                    this.forwardContextResponse(message);
                }
                break;
                
            case 'config_update':
                this.updateConfiguration(message.config);
                break;
                
            case 'module_control':
                this.handleModuleControl(message);
                break;
                
            case 'proactive_suggestion':
                this.handleProactiveSuggestion(message);
                break;
                
            default:
                console.log('Received message from LAS:', message);
        }
    }

    addToContextBuffer(entry) {
        this.contextBuffer.push(entry);
        
        // Keep buffer size manageable (last 100 entries)
        if (this.contextBuffer.length > 100) {
            this.contextBuffer = this.contextBuffer.slice(-100);
        }
        
        // Store in local storage for persistence
        chrome.storage.local.set({ 
            contextBuffer: this.contextBuffer.slice(-50) // Store last 50 for efficiency
        });
    }

    send(message) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.warn('Cannot send message - LAS not connected');
            this.queueMessage(message);
        }
    }

    queueMessage(message) {
        this.messageQueue.push(message);
        
        // Limit queue size
        if (this.messageQueue.length > 1000) {
            this.messageQueue = this.messageQueue.slice(-500);
        }
    }

    processMessageQueue() {
        while (this.messageQueue.length > 0 && this.isConnected) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }

    setupPeriodicSync() {
        // Sync context buffer every 30 seconds
        setInterval(() => {
            if (this.isConnected && this.contextBuffer.length > 0) {
                this.send({
                    type: 'bulk_context_sync',
                    entries: this.contextBuffer.slice(-20), // Send last 20 entries
                    timestamp: Date.now()
                });
            }
        }, 30000);
    }

    updatePreferences(preferences) {
        this.userPreferences = { ...this.userPreferences, ...preferences };
        chrome.storage.local.set({ userPreferences: this.userPreferences });
        
        // Notify all tabs of preference changes
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'preferencesUpdated',
                    preferences: this.userPreferences
                });
            });
        });
        
        // Notify LAS
        if (this.isConnected) {
            this.send({
                type: 'preferences_update',
                preferences: this.userPreferences
            });
        }
    }

    toggleModule(moduleName, enabled) {
        if (this.modules[moduleName] !== undefined) {
            this.modules[moduleName].enabled = enabled;
            
            // Special handling for morphcast
            if (moduleName === 'morphcast') {
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            type: 'toggleMorphcast',
                            enabled: enabled
                        });
                    }
                });
            }
            
            // Notify LAS
            if (this.isConnected) {
                this.send({
                    type: 'module_status_update',
                    module: moduleName,
                    enabled: enabled
                });
            }
        }
    }

    requestContextFromLAS(query, sendResponse) {
        const requestId = Date.now().toString();
        
        // Store callback for later
        this.pendingRequests = this.pendingRequests || {};
        this.pendingRequests[requestId] = sendResponse;
        
        this.send({
            type: 'context_request',
            requestId: requestId,
            query: query
        });
        
        // Timeout after 5 seconds
        setTimeout(() => {
            if (this.pendingRequests[requestId]) {
                this.pendingRequests[requestId]({ error: 'Request timeout' });
                delete this.pendingRequests[requestId];
            }
        }, 5000);
    }

    forwardContextResponse(message) {
        if (this.pendingRequests && this.pendingRequests[message.requestId]) {
            this.pendingRequests[message.requestId](message.data);
            delete this.pendingRequests[message.requestId];
        }
    }

    handleProactiveSuggestion(message) {
        // Create a notification or update the extension badge
        chrome.action.setBadgeText({ text: '!' });
        chrome.action.setBadgeBackgroundColor({ color: '#667eea' });
        
        // Store the suggestion
        chrome.storage.local.set({ 
            latestProactiveSuggestion: message.suggestion,
            hasUnreadSuggestion: true
        });
    }

    broadcastConnectionStatus(status) {
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    type: 'lasConnectionStatus',
                    status: status
                });
            });
        });
    }

    updateConfiguration(config) {
        // Update local configuration based on LAS instructions
        if (config.modules) {
            Object.keys(config.modules).forEach(module => {
                if (this.modules[module]) {
                    this.modules[module] = { ...this.modules[module], ...config.modules[module] };
                }
            });
        }
        
        console.log('Configuration updated from LAS:', config);
    }

    handleModuleControl(message) {
        // LAS can remotely control modules
        if (message.action === 'enable') {
            this.toggleModule(message.module, true);
        } else if (message.action === 'disable') {
            this.toggleModule(message.module, false);
        }
    }

    async getSessionStats(sendResponse) {
        try {
            // Get stats from storage
            const stored = await chrome.storage.local.get(['sessionStats']);
            const stats = stored.sessionStats || {
                selections: 0,
                copies: 0,
                focusPercentage: 100,
                scrollDepth: 0,
                clicks: 0
            };

            // Add real-time data
            stats.contextBufferSize = this.contextBuffer.length;
            stats.isConnected = this.isConnected;
            stats.queuedMessages = this.messageQueue.length;

            sendResponse(stats);
        } catch (error) {
            console.error('Failed to get session stats:', error);
            sendResponse({ error: 'Failed to get session stats' });
        }
    }

    async testCommunication(sendResponse) {
        try {
            // Test communication with all active tabs
            const tabs = await chrome.tabs.query({});
            const results = [];

            for (const tab of tabs) {
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, { type: 'ping' });
                    results.push({
                        tabId: tab.id,
                        url: tab.url,
                        title: tab.title,
                        status: 'success',
                        response: response
                    });
                } catch (error) {
                    results.push({
                        tabId: tab.id,
                        url: tab.url,
                        title: tab.title,
                        status: 'error',
                        error: error.message
                    });
                }
            }

            sendResponse({
                status: 'Communication test completed',
                isConnectedToLAS: this.isConnected,
                totalTabs: tabs.length,
                results: results,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('Failed to test communication:', error);
            sendResponse({ error: 'Failed to test communication', details: error.message });
        }
    }
}

// Initialize the background service
const hraBackground = new HRABackground();

// Keep service worker alive
chrome.runtime.onInstalled.addListener(() => {
    console.log('HRA Chrome Extension installed');
});
