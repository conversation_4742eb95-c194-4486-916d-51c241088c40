// HRA Chrome Extension - Content Script
// Handles text capture, behavioral analysis, and MorphCast emotion detection

class HRAContentScript {
    constructor() {
        this.isActive = false;
        this.userPreferences = {};
        this.modules = {
            textCapture: true,
            morphcast: false,
            behavioral: true
        };
        
        // Text capture state
        this.selectedText = '';
        this.lastCapturedText = '';
        this.captureMode = 'selective'; // 'all' or 'selective'
        
        // Behavioral tracking state
        this.scrollData = {
            position: 0,
            velocity: 0,
            direction: null,
            lastUpdate: Date.now(),
            pauses: []
        };
        
        this.clickData = {
            clickCount: 0,
            lastClick: null,
            clickPattern: []
        };
        
        this.textInteractionData = {
            selections: 0,
            averageSelectionLength: 0,
            copyEvents: 0
        };
        
        // Page engagement metrics
        this.pageEngagement = {
            startTime: Date.now(),
            focusTime: 0,
            lastFocusStart: Date.now(),
            visibilityChanges: 0,
            scrollDepth: 0,
            maxScrollDepth: 0
        };
        
        // Privacy protection
        this.sensitivePatterns = [
            /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit cards
            /\b\d{3}[\s-]?\d{2}[\s-]?\d{4}\b/, // SSN
            /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email
            /\b\d{10,15}\b/ // Phone numbers
        ];
        
        this.initialize();
    }

    async initialize() {
        try {
            // Check if extension context is valid
            if (!chrome.runtime?.id) {
                console.warn('HRA: Extension context invalidated, skipping initialization');
                return;
            }

            // Load preferences from background with error handling
            try {
                const response = await chrome.runtime.sendMessage({ type: 'getLASStatus' });
                console.log('HRA: Background connection established');
            } catch (error) {
                console.warn('HRA: Could not connect to background script:', error.message);
                // Continue initialization even if background is unavailable
            }

            // Set up event listeners
            this.setupEventListeners();

            // Initialize behavioral tracking
            this.initializeBehavioralTracking();

            // Set up message listener for background communication
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                this.handleMessage(request, sender, sendResponse);
            });

            this.isActive = true;
            console.log('HRA Content Script initialized on:', window.location.href);
        } catch (error) {
            console.error('HRA: Failed to initialize content script:', error);
        }
    }

    handleMessage(request, sender, sendResponse) {
        console.log("Message received in content script:", request);

        try {
            switch (request.type) {
                case 'preferencesUpdated':
                    this.updatePreferences(request.preferences);
                    sendResponse({ status: "Preferences updated successfully" });
                    break;

                case 'toggleMorphcast':
                    this.toggleMorphcast(request.enabled);
                    sendResponse({ status: `MorphCast ${request.enabled ? 'enabled' : 'disabled'} successfully` });
                    break;

                case 'lasConnectionStatus':
                    this.handleLASStatusChange(request.status);
                    sendResponse({ status: "LAS status updated successfully" });
                    break;

                case 'capturePageContent':
                    this.capturePageContent();
                    sendResponse({ status: "Page content captured successfully" });
                    break;

                case 'ping':
                    sendResponse({ status: "Content script is active", url: window.location.href });
                    break;

                default:
                    console.log('Unknown message type:', request.type);
                    sendResponse({ status: "Unknown message type", type: request.type });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ status: "Error handling message", error: error.message });
        }

        return true; // Indicates we will send a response asynchronously
    }

    setupEventListeners() {
        // Text selection events
        document.addEventListener('mouseup', () => {
            if (this.modules.textCapture) {
                this.handleTextSelection();
            }
        });

        document.addEventListener('keyup', (e) => {
            if (this.modules.textCapture && (e.ctrlKey || e.metaKey) && e.key === 'c') {
                this.handleCopyEvent();
            }
        });

        // Behavioral tracking events
        if (this.modules.behavioral) {
            // Scroll tracking
            let scrollTimeout;
            window.addEventListener('scroll', (e) => {
                this.trackScrollBehavior();
                
                // Detect scroll pauses
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    this.recordScrollPause();
                }, 150);
            });

            // Click tracking
            document.addEventListener('click', (e) => {
                this.trackClickBehavior(e);
            });

            // Focus and visibility tracking
            window.addEventListener('focus', () => {
                this.pageEngagement.lastFocusStart = Date.now();
            });

            window.addEventListener('blur', () => {
                if (this.pageEngagement.lastFocusStart) {
                    this.pageEngagement.focusTime += Date.now() - this.pageEngagement.lastFocusStart;
                }
            });

            document.addEventListener('visibilitychange', () => {
                this.pageEngagement.visibilityChanges++;
                if (document.hidden) {
                    if (this.pageEngagement.lastFocusStart) {
                        this.pageEngagement.focusTime += Date.now() - this.pageEngagement.lastFocusStart;
                    }
                } else {
                    this.pageEngagement.lastFocusStart = Date.now();
                }
                
                this.sendBehavioralUpdate('visibility', {
                    hidden: document.hidden,
                    visibilityChanges: this.pageEngagement.visibilityChanges
                });
            });

            // Form interaction tracking
            document.addEventListener('input', (e) => {
                if (e.target.tagName.toLowerCase() === 'input' || 
                    e.target.tagName.toLowerCase() === 'textarea') {
                    this.trackFormInteraction(e);
                }
            });
        }

        // Page load completion
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.onPageReady();
            });
        } else {
            this.onPageReady();
        }
    }

    initializeBehavioralTracking() {
        // Initialize scroll depth tracking
        this.updateScrollDepth();
        
        // Set up periodic engagement reports
        setInterval(() => {
            this.reportPageEngagement();
        }, 30000); // Every 30 seconds
        
        // Track initial page metrics
        this.captureInitialPageMetrics();
    }

    handleTextSelection() {
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();
        
        if (selectedText.length > 0 && selectedText !== this.lastCapturedText) {
            // Privacy check
            if (this.containsSensitiveInfo(selectedText)) {
                console.log('Sensitive information detected in selection - not capturing');
                return;
            }
            
            this.selectedText = selectedText;
            this.lastCapturedText = selectedText;
            
            // Update text interaction metrics
            this.textInteractionData.selections++;
            const avgLength = this.textInteractionData.averageSelectionLength;
            this.textInteractionData.averageSelectionLength = 
                (avgLength * (this.textInteractionData.selections - 1) + selectedText.length) / 
                this.textInteractionData.selections;
            
            // Get context about the selected element
            const element = selection.anchorNode.parentElement;
            const elementContext = this.getElementContext(element);
            
            this.sendTextCapture({
                text: selectedText,
                elementType: elementContext.type,
                elementClass: elementContext.className,
                elementId: elementContext.id,
                action: 'selection',
                context: this.getPageContext(),
                timestamp: Date.now()
            });
        }
    }

    handleCopyEvent() {
        if (this.selectedText.length > 0) {
            this.textInteractionData.copyEvents++;
            
            this.sendTextCapture({
                text: this.selectedText,
                action: 'copy',
                context: this.getPageContext(),
                timestamp: Date.now()
            });
        }
    }

    trackScrollBehavior() {
        const currentPosition = window.pageYOffset;
        const currentTime = Date.now();
        const timeDelta = currentTime - this.scrollData.lastUpdate;
        
        if (timeDelta > 0) {
            const positionDelta = currentPosition - this.scrollData.position;
            const velocity = Math.abs(positionDelta) / timeDelta;
            
            this.scrollData.velocity = velocity;
            this.scrollData.direction = positionDelta > 0 ? 'down' : 'up';
            this.scrollData.position = currentPosition;
            this.scrollData.lastUpdate = currentTime;
            
            // Update scroll depth
            this.updateScrollDepth();
        }
    }

    recordScrollPause() {
        const pause = {
            position: this.scrollData.position,
            timestamp: Date.now(),
            duration: null // Will be calculated when scroll resumes
        };
        
        this.scrollData.pauses.push(pause);
        
        // Keep only recent pauses (last 10)
        if (this.scrollData.pauses.length > 10) {
            this.scrollData.pauses = this.scrollData.pauses.slice(-10);
        }
        
        this.sendBehavioralUpdate('scroll_pause', {
            position: pause.position,
            scrollDepth: this.pageEngagement.scrollDepth,
            timestamp: pause.timestamp
        });
    }

    trackClickBehavior(event) {
        const currentTime = Date.now();
        const clickInfo = {
            x: event.clientX,
            y: event.clientY,
            target: event.target.tagName.toLowerCase(),
            targetClass: event.target.className,
            targetId: event.target.id,
            timestamp: currentTime
        };
        
        this.clickData.clickCount++;
        this.clickData.lastClick = clickInfo;
        this.clickData.clickPattern.push(clickInfo);
        
        // Keep pattern history manageable
        if (this.clickData.clickPattern.length > 20) {
            this.clickData.clickPattern = this.clickData.clickPattern.slice(-20);
        }
        
        this.sendBehavioralUpdate('click', clickInfo);
    }

    trackFormInteraction(event) {
        const formData = {
            inputType: event.target.type,
            inputName: event.target.name,
            inputId: event.target.id,
            valueLength: event.target.value.length,
            timestamp: Date.now()
        };
        
        // Don't capture actual form values for privacy
        this.sendBehavioralUpdate('form_interaction', formData);
    }

    updateScrollDepth() {
        const scrollPosition = window.pageYOffset;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        
        const scrollPercentage = Math.round((scrollPosition + windowHeight) / documentHeight * 100);
        this.pageEngagement.scrollDepth = Math.min(scrollPercentage, 100);
        this.pageEngagement.maxScrollDepth = Math.max(
            this.pageEngagement.maxScrollDepth, 
            this.pageEngagement.scrollDepth
        );
    }

    reportPageEngagement() {
        const currentTime = Date.now();
        const sessionDuration = currentTime - this.pageEngagement.startTime;
        
        // Calculate current focus time
        let totalFocusTime = this.pageEngagement.focusTime;
        if (!document.hidden && this.pageEngagement.lastFocusStart) {
            totalFocusTime += currentTime - this.pageEngagement.lastFocusStart;
        }
        
        const engagementReport = {
            sessionDuration,
            focusTime: totalFocusTime,
            focusPercentage: Math.round((totalFocusTime / sessionDuration) * 100),
            scrollDepth: this.pageEngagement.scrollDepth,
            maxScrollDepth: this.pageEngagement.maxScrollDepth,
            visibilityChanges: this.pageEngagement.visibilityChanges,
            clickCount: this.clickData.clickCount,
            textSelections: this.textInteractionData.selections,
            averageSelectionLength: this.textInteractionData.averageSelectionLength,
            copyEvents: this.textInteractionData.copyEvents,
            url: window.location.href,
            title: document.title
        };
        
        this.sendBehavioralUpdate('engagement_report', engagementReport);
    }

    captureInitialPageMetrics() {
        const pageMetrics = {
            url: window.location.href,
            title: document.title,
            referrer: document.referrer,
            loadTime: Date.now() - this.pageEngagement.startTime,
            documentHeight: document.documentElement.scrollHeight,
            viewportHeight: window.innerHeight,
            timestamp: Date.now()
        };
        
        this.sendBehavioralUpdate('page_load', pageMetrics);
    }

    capturePageContent() {
        // Extract meaningful content from the page
        const content = {
            title: document.title,
            headings: this.extractHeadings(),
            mainContent: this.extractMainContent(),
            links: this.extractLinks(),
            images: this.extractImages(),
            timestamp: Date.now()
        };
        
        this.sendTextCapture({
            text: JSON.stringify(content),
            action: 'page_content',
            context: this.getPageContext(),
            timestamp: Date.now()
        });
    }

    extractHeadings() {
        const headings = [];
        document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
            headings.push({
                level: heading.tagName.toLowerCase(),
                text: heading.textContent.trim(),
                id: heading.id
            });
        });
        return headings;
    }

    extractMainContent() {
        // Try to find main content areas
        const contentSelectors = [
            'main', 'article', '.content', '.main-content', 
            '#content', '#main', '.post-content', '.entry-content'
        ];
        
        for (const selector of contentSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element.textContent.trim().substring(0, 2000); // Limit length
            }
        }
        
        // Fallback to body content
        return document.body.textContent.trim().substring(0, 2000);
    }

    extractLinks() {
        const links = [];
        document.querySelectorAll('a[href]').forEach(link => {
            links.push({
                text: link.textContent.trim(),
                href: link.href,
                title: link.title
            });
        });
        return links.slice(0, 20); // Limit to first 20 links
    }

    extractImages() {
        const images = [];
        document.querySelectorAll('img[src]').forEach(img => {
            images.push({
                src: img.src,
                alt: img.alt,
                title: img.title
            });
        });
        return images.slice(0, 10); // Limit to first 10 images
    }

    getElementContext(element) {
        return {
            type: element.tagName.toLowerCase(),
            className: element.className,
            id: element.id,
            textContent: element.textContent.trim().substring(0, 100)
        };
    }

    getPageContext() {
        return {
            url: window.location.href,
            title: document.title,
            domain: window.location.hostname,
            timestamp: Date.now()
        };
    }

    containsSensitiveInfo(text) {
        return this.sensitivePatterns.some(pattern => pattern.test(text));
    }

    // MorphCast integration methods
    toggleMorphcast(enabled) {
        this.modules.morphcast = enabled;
        
        if (enabled) {
            this.initializeMorphcast();
        } else {
            this.destroyMorphcast();
        }
    }

    initializeMorphcast() {
        // Inject MorphCast loader script
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('inject.js');
        script.onload = () => {
            console.log('MorphCast injection script loaded');
            // The inject.js will handle the actual MorphCast initialization
        };
        (document.head || document.documentElement).appendChild(script);
    }

    destroyMorphcast() {
        // Send message to injected script to stop MorphCast
        window.postMessage({ type: 'STOP_MORPHCAST' }, '*');
        
        // Listen for MorphCast messages
        window.addEventListener('message', (event) => {
            if (event.source === window && event.data) {
                switch (event.data.type) {
                    case 'MORPHCAST_STOPPED':
                        console.log('MorphCast stopped successfully');
                        break;
                    case 'MORPHCAST_UNAVAILABLE':
                        console.warn('MorphCast unavailable:', event.data.error);
                        this.modules.morphcast = false;
                        break;
                    case 'EMOTION_UPDATE':
                        this.handleEmotionUpdate(event.data.data);
                        break;
                    default:
                        // Handle other MorphCast messages
                        break;
                }
            }
        });
    }

    handleEmotionUpdate(emotionData) {
        // Process emotion data from MorphCast
        if (emotionData && this.modules.morphcast) {
            // Send emotion data to background script
            chrome.runtime.sendMessage({
                type: 'emotionData',
                data: emotionData
            });
        }
    }

    onPageReady() {
        // Page is fully loaded, capture initial state
        this.captureInitialPageMetrics();
        
        // If auto-capture is enabled, capture page content
        if (this.captureMode === 'all') {
            setTimeout(() => {
                this.capturePageContent();
            }, 2000); // Wait 2 seconds for dynamic content to load
        }
    }

    updatePreferences(preferences) {
        this.userPreferences = preferences;
        this.captureMode = preferences.captureMode || 'selective';
        
        // Update module states based on preferences
        if (preferences.emotionCapture !== undefined) {
            this.modules.morphcast = preferences.emotionCapture;
        }
    }

    handleLASStatusChange(status) {
        console.log('LAS connection status changed:', status);
        
        // Update UI indicator if needed
        if (status === 'connected') {
            this.showConnectionIndicator(true);
        } else {
            this.showConnectionIndicator(false);
        }
    }

    showConnectionIndicator(connected) {
        // Remove existing indicator
        const existingIndicator = document.getElementById('hra-connection-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        // Create new indicator
        const indicator = document.createElement('div');
        indicator.id = 'hra-connection-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
            background-color: ${connected ? '#4CAF50' : '#f44336'};
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;
        indicator.textContent = `HRA ${connected ? 'Connected' : 'Disconnected'}`;
        
        document.body.appendChild(indicator);
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    if (indicator.parentNode) {
                        indicator.remove();
                    }
                }, 300);
            }
        }, 3000);
    }

    // Communication methods
    sendTextCapture(data) {
        chrome.runtime.sendMessage({
            type: 'capturedText',
            data: data
        });
    }

    sendBehavioralUpdate(eventType, data) {
        chrome.runtime.sendMessage({
            type: 'behavioralData',
            data: {
                eventType,
                ...data
            }
        });
    }

    sendEmotionData(data) {
        chrome.runtime.sendMessage({
            type: 'emotionData',
            data: data
        });
    }
}

// Initialize content script
if (window.location.href.startsWith('http')) {
    const hraContent = new HRAContentScript();
    
    // Make it globally accessible for inject.js communication
    window.hraContent = hraContent;
}
