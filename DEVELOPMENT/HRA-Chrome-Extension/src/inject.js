// HRA (Harmonic Resonance Agent) - MorphCast Integration Injection Script
// This script runs in the page context to access camera and MorphCast SDK

class HRAMorphCastModule {
    constructor() {
        this.isInitialized = false;
        this.isRunning = false;
        this.morphcastSDK = null;
        this.stream = null;
        this.canvas = null;
        this.context = null;
        this.video = null;
        
        // License configuration
        this.config = {
            licenseKey: 'sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b',
            email: 'gabemc<PERSON><EMAIL>'
        };
        
        // Emotion tracking state
        this.emotionHistory = [];
        this.currentEmotionState = null;
        this.sessionStartTime = Date.now();
        this.lastEmotionUpdate = 0;
        
        // <PERSON>'s Circumplex Model mapping
        this.emotionCircumplex = {
            // High Arousal, Positive Valence
            'excited': { arousal: 0.8, valence: 0.8, quadrant: 'high_positive' },
            'elated': { arousal: 0.9, valence: 0.9, quadrant: 'high_positive' },
            'ecstatic': { arousal: 1.0, valence: 0.9, quadrant: 'high_positive' },
            
            // High Arousal, Negative Valence  
            'angry': { arousal: 0.8, valence: -0.7, quadrant: 'high_negative' },
            'frustrated': { arousal: 0.7, valence: -0.5, quadrant: 'high_negative' },
            'annoyed': { arousal: 0.6, valence: -0.4, quadrant: 'high_negative' },
            
            // Low Arousal, Positive Valence
            'calm': { arousal: -0.6, valence: 0.5, quadrant: 'low_positive' },
            'peaceful': { arousal: -0.8, valence: 0.7, quadrant: 'low_positive' },
            'content': { arousal: -0.4, valence: 0.6, quadrant: 'low_positive' },
            
            // Low Arousal, Negative Valence
            'sad': { arousal: -0.6, valence: -0.6, quadrant: 'low_negative' },
            'depressed': { arousal: -0.8, valence: -0.8, quadrant: 'low_negative' },
            'melancholy': { arousal: -0.5, valence: -0.4, quadrant: 'low_negative' },
            
            // Neutral states
            'neutral': { arousal: 0.0, valence: 0.0, quadrant: 'neutral' },
            'focused': { arousal: 0.2, valence: 0.1, quadrant: 'neutral' }
        };
        
        // Auto-stop configuration
        this.autoStopTimeout = 15 * 60 * 1000; // 15 minutes
        this.autoStopTimer = null;
        
        this.initialize();
    }

    async initialize() {
        try {
            // Listen for messages from content script
            window.addEventListener('message', (event) => {
                if (event.source === window && event.data) {
                    this.handleMessage(event.data);
                }
            });
            
            // Load MorphCast SDK
            await this.loadMorphCastSDK();
            
            console.log('HRA MorphCast Module initialized');
            this.isInitialized = true;
            
        } catch (error) {
            console.error('Failed to initialize HRA MorphCast Module:', error);
        }
    }

    async loadMorphCastSDK() {
        return new Promise((resolve, reject) => {
            if (window.MorphCast) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js';
            script.onload = () => {
                console.log('MorphCast SDK loaded successfully');
                resolve();
            };
            script.onerror = () => {
                reject(new Error('Failed to load MorphCast SDK'));
            };
            
            document.head.appendChild(script);
        });
    }

    handleMessage(data) {
        switch (data.type) {
            case 'START_MORPHCAST':
                this.startEmotionCapture();
                break;
                
            case 'STOP_MORPHCAST':
                this.stopEmotionCapture();
                break;
                
            case 'GET_EMOTION_STATE':
                this.sendCurrentEmotionState();
                break;
                
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    async startEmotionCapture() {
        if (this.isRunning || !this.isInitialized) {
            console.warn('MorphCast already running or not initialized');
            return;
        }

        try {
            // Request camera permission
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                } 
            });
            
            // Create video element (hidden)
            this.video = document.createElement('video');
            this.video.style.display = 'none';
            this.video.autoplay = true;
            this.video.muted = true;
            this.video.srcObject = this.stream;
            document.body.appendChild(this.video);
            
            // Create canvas for processing
            this.canvas = document.createElement('canvas');
            this.canvas.width = 640;
            this.canvas.height = 480;
            this.canvas.style.display = 'none';
            this.context = this.canvas.getContext('2d');
            document.body.appendChild(this.canvas);
            
            // Wait for video to be ready
            await new Promise((resolve) => {
                this.video.onloadedmetadata = resolve;
            });
            
            // Initialize MorphCast SDK
            await this.initializeMorphCast();
            
            this.isRunning = true;
            this.sessionStartTime = Date.now();
            
            // Set up auto-stop timer
            this.autoStopTimer = setTimeout(() => {
                console.log('Auto-stopping MorphCast after 15 minutes');
                this.stopEmotionCapture();
            }, this.autoStopTimeout);
            
            // Start emotion detection loop
            this.startEmotionDetectionLoop();
            
            console.log('HRA emotion capture started successfully');
            
            // Notify content script
            this.sendMessage({ type: 'MORPHCAST_STARTED' });
            
        } catch (error) {
            console.error('Failed to start emotion capture:', error);
            this.cleanup();
        }
    }

    async initializeMorphCast() {
        if (!window.MorphCast) {
            throw new Error('MorphCast SDK not loaded');
        }

        // Initialize SDK with license
        this.morphcastSDK = await window.MorphCast.init({
            license: this.config.licenseKey,
            email: this.config.email,
            modules: [
                window.MorphCast.Modules.FACE_EMOTION,
                window.MorphCast.Modules.FACE_AROUSAL_VALENCE,
                window.MorphCast.Modules.FACE_ATTENTION,
                window.MorphCast.Modules.FACE_POSE,
                window.MorphCast.Modules.FACE_AGE,
                window.MorphCast.Modules.FACE_GENDER,
                window.MorphCast.Modules.FACE_FEATURES
            ],
            alarms: [
                window.MorphCast.Alarms.LOW_ATTENTION,
                window.MorphCast.Alarms.NO_FACE,
                window.MorphCast.Alarms.MULTIPLE_FACES
            ]
        });

        // Configure data aggregator
        this.morphcastSDK.addModule(window.MorphCast.Modules.DATA_AGGREGATOR, {
            enabled: true,
            period: 5000 // 5 seconds
        });

        console.log('MorphCast SDK initialized with modules');
    }

    startEmotionDetectionLoop() {
        const processFrame = () => {
            if (!this.isRunning || !this.video || !this.morphcastSDK) {
                return;
            }

            try {
                // Draw video frame to canvas
                this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
                
                // Get image data
                const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
                
                // Process with MorphCast
                this.morphcastSDK.analyze(imageData.data, this.canvas.width, this.canvas.height)
                    .then((results) => {
                        this.processEmotionResults(results);
                    })
                    .catch((error) => {
                        console.error('MorphCast analysis error:', error);
                    });
                
            } catch (error) {
                console.error('Frame processing error:', error);
            }
            
            // Continue loop
            if (this.isRunning) {
                requestAnimationFrame(processFrame);
            }
        };

        processFrame();
    }

    processEmotionResults(results) {
        const currentTime = Date.now();
        
        if (!results || !results.emotion) {
            return;
        }

        // Extract emotion data
        const emotionData = {
            timestamp: currentTime,
            sessionTime: currentTime - this.sessionStartTime,
            
            // Basic emotions
            emotions: results.emotion || {},
            
            // Arousal and Valence
            arousal: results.arousal_valence?.arousal || 0,
            valence: results.arousal_valence?.valence || 0,
            
            // Attention and engagement
            attention: results.attention?.attention || 0,
            
            // Face metrics
            facePresent: results.face_present || false,
            faceCount: results.face_count || 0,
            
            // Demographics (if available)
            age: results.age?.age || null,
            gender: results.gender?.gender || null,
            
            // Pose information
            pose: results.pose || null
        };

        // Map to Russell's Circumplex Model
        const dominantEmotion = this.getDominantEmotion(emotionData.emotions);
        const circumplexMapping = this.mapToCircumplex(dominantEmotion, emotionData.arousal, emotionData.valence);
        
        emotionData.dominant_emotion = dominantEmotion;
        emotionData.circumplex = circumplexMapping;
        emotionData.emotional_state = this.classifyEmotionalState(emotionData);

        // Update history
        this.emotionHistory.push(emotionData);
        this.currentEmotionState = emotionData;
        
        // Keep history manageable (last 100 entries)
        if (this.emotionHistory.length > 100) {
            this.emotionHistory = this.emotionHistory.slice(-100);
        }

        // Send to content script (throttled to every 200ms)
        if (currentTime - this.lastEmotionUpdate > 200) {
            this.sendEmotionUpdate(emotionData);
            this.lastEmotionUpdate = currentTime;
        }

        // Handle alarms
        this.processAlarms(results.alarms);
    }

    getDominantEmotion(emotions) {
        if (!emotions || typeof emotions !== 'object') {
            return 'neutral';
        }

        let maxEmotion = 'neutral';
        let maxValue = 0;

        Object.keys(emotions).forEach(emotion => {
            if (emotions[emotion] > maxValue) {
                maxValue = emotions[emotion];
                maxEmotion = emotion;
            }
        });

        return maxValue > 0.3 ? maxEmotion : 'neutral'; // Threshold for significance
    }

    mapToCircumplex(emotion, arousal, valence) {
        // Use predefined mapping if available
        if (this.emotionCircumplex[emotion]) {
            return this.emotionCircumplex[emotion];
        }

        // Fallback to arousal/valence values
        const quadrant = this.determineQuadrant(arousal, valence);
        
        return {
            arousal: arousal,
            valence: valence,
            quadrant: quadrant
        };
    }

    determineQuadrant(arousal, valence) {
        if (arousal > 0.2 && valence > 0.2) return 'high_positive';
        if (arousal > 0.2 && valence < -0.2) return 'high_negative';
        if (arousal < -0.2 && valence > 0.2) return 'low_positive';
        if (arousal < -0.2 && valence < -0.2) return 'low_negative';
        return 'neutral';
    }

    classifyEmotionalState(emotionData) {
        const { arousal, valence, attention, dominant_emotion } = emotionData;
        
        // Comprehensive emotional state classification
        let state = {
            energy_level: this.classifyEnergyLevel(arousal),
            mood_valence: this.classifyMoodValence(valence),
            engagement_level: this.classifyEngagementLevel(attention),
            primary_emotion: dominant_emotion,
            stability: this.calculateEmotionalStability(),
            intensity: Math.sqrt(arousal * arousal + valence * valence)
        };

        return state;
    }

    classifyEnergyLevel(arousal) {
        if (arousal > 0.6) return 'high';
        if (arousal > 0.2) return 'medium';
        if (arousal > -0.2) return 'balanced';
        if (arousal > -0.6) return 'low';
        return 'very_low';
    }

    classifyMoodValence(valence) {
        if (valence > 0.6) return 'very_positive';
        if (valence > 0.2) return 'positive';
        if (valence > -0.2) return 'neutral';
        if (valence > -0.6) return 'negative';
        return 'very_negative';
    }

    classifyEngagementLevel(attention) {
        if (attention > 0.8) return 'highly_engaged';
        if (attention > 0.6) return 'engaged';
        if (attention > 0.4) return 'moderately_engaged';
        if (attention > 0.2) return 'low_engagement';
        return 'disengaged';
    }

    calculateEmotionalStability() {
        if (this.emotionHistory.length < 5) {
            return 'insufficient_data';
        }

        // Calculate variance in recent emotion values
        const recentEmotions = this.emotionHistory.slice(-5);
        const arousalVariance = this.calculateVariance(recentEmotions.map(e => e.arousal));
        const valenceVariance = this.calculateVariance(recentEmotions.map(e => e.valence));
        
        const totalVariance = arousalVariance + valenceVariance;
        
        if (totalVariance < 0.1) return 'very_stable';
        if (totalVariance < 0.3) return 'stable';
        if (totalVariance < 0.6) return 'moderately_stable';
        if (totalVariance < 1.0) return 'unstable';
        return 'very_unstable';
    }

    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    }

    processAlarms(alarms) {
        if (!alarms) return;

        Object.keys(alarms).forEach(alarmType => {
            if (alarms[alarmType]) {
                this.handleAlarm(alarmType);
            }
        });
    }

    handleAlarm(alarmType) {
        const alarmData = {
            type: alarmType,
            timestamp: Date.now(),
            sessionTime: Date.now() - this.sessionStartTime
        };

        console.log('MorphCast alarm triggered:', alarmType);
        
        // Send alarm to content script
        this.sendMessage({
            type: 'MORPHCAST_ALARM',
            alarm: alarmData
        });
    }

    sendEmotionUpdate(emotionData) {
        // Send to content script via window messaging
        this.sendMessage({
            type: 'EMOTION_UPDATE',
            data: emotionData
        });
    }

    sendCurrentEmotionState() {
        if (this.currentEmotionState) {
            this.sendMessage({
                type: 'CURRENT_EMOTION_STATE',
                data: this.currentEmotionState
            });
        }
    }

    sendMessage(message) {
        window.postMessage(message, '*');
    }

    stopEmotionCapture() {
        console.log('Stopping HRA emotion capture');
        
        this.isRunning = false;
        
        // Clear auto-stop timer
        if (this.autoStopTimer) {
            clearTimeout(this.autoStopTimer);
            this.autoStopTimer = null;
        }
        
        // Cleanup resources
        this.cleanup();
        
        // Send final emotion summary
        this.sendEmotionSummary();
        
        // Notify content script
        this.sendMessage({ type: 'MORPHCAST_STOPPED' });
    }

    cleanup() {
        // Stop video stream
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        // Remove video element
        if (this.video && this.video.parentNode) {
            this.video.parentNode.removeChild(this.video);
            this.video = null;
        }

        // Remove canvas
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
            this.canvas = null;
        }

        // Cleanup MorphCast SDK
        if (this.morphcastSDK) {
            this.morphcastSDK.stop();
            this.morphcastSDK = null;
        }

        this.context = null;
    }

    sendEmotionSummary() {
        if (this.emotionHistory.length === 0) {
            return;
        }

        const sessionDuration = Date.now() - this.sessionStartTime;
        const summary = this.generateEmotionSummary(sessionDuration);
        
        this.sendMessage({
            type: 'EMOTION_SESSION_SUMMARY',
            data: summary
        });
    }

    generateEmotionSummary(sessionDuration) {
        const emotions = this.emotionHistory;
        const totalFrames = emotions.length;
        
        // Calculate averages
        const avgArousal = emotions.reduce((sum, e) => sum + e.arousal, 0) / totalFrames;
        const avgValence = emotions.reduce((sum, e) => sum + e.valence, 0) / totalFrames;
        const avgAttention = emotions.reduce((sum, e) => sum + e.attention, 0) / totalFrames;
        
        // Find dominant emotions throughout session
        const emotionCounts = {};
        emotions.forEach(e => {
            const emotion = e.dominant_emotion;
            emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
        });
        
        const dominantEmotions = Object.keys(emotionCounts)
            .sort((a, b) => emotionCounts[b] - emotionCounts[a])
            .slice(0, 3);

        // Calculate emotional journey
        const emotionalJourney = this.calculateEmotionalJourney();
        
        return {
            sessionDuration,
            totalFrames,
            averages: {
                arousal: avgArousal,
                valence: avgValence,
                attention: avgAttention
            },
            dominantEmotions,
            emotionDistribution: emotionCounts,
            emotionalJourney,
            stability: this.calculateOverallStability(),
            engagement: this.calculateOverallEngagement()
        };
    }

    calculateEmotionalJourney() {
        // Divide session into segments and track emotional progression
        const segments = 5;
        const segmentSize = Math.floor(this.emotionHistory.length / segments);
        const journey = [];
        
        for (let i = 0; i < segments; i++) {
            const start = i * segmentSize;
            const end = Math.min(start + segmentSize, this.emotionHistory.length);
            const segment = this.emotionHistory.slice(start, end);
            
            if (segment.length > 0) {
                const avgArousal = segment.reduce((sum, e) => sum + e.arousal, 0) / segment.length;
                const avgValence = segment.reduce((sum, e) => sum + e.valence, 0) / segment.length;
                
                journey.push({
                    segment: i + 1,
                    arousal: avgArousal,
                    valence: avgValence,
                    quadrant: this.determineQuadrant(avgArousal, avgValence)
                });
            }
        }
        
        return journey;
    }

    calculateOverallStability() {
        const arousalValues = this.emotionHistory.map(e => e.arousal);
        const valenceValues = this.emotionHistory.map(e => e.valence);
        
        const arousalVariance = this.calculateVariance(arousalValues);
        const valenceVariance = this.calculateVariance(valenceValues);
        
        return {
            arousal_stability: arousalVariance,
            valence_stability: valenceVariance,
            overall_stability: (arousalVariance + valenceVariance) / 2
        };
    }

    calculateOverallEngagement() {
        const attentionValues = this.emotionHistory.map(e => e.attention);
        const avgAttention = attentionValues.reduce((sum, val) => sum + val, 0) / attentionValues.length;
        const maxAttention = Math.max(...attentionValues);
        const minAttention = Math.min(...attentionValues);
        
        return {
            average: avgAttention,
            maximum: maxAttention,
            minimum: minAttention,
            range: maxAttention - minAttention
        };
    }
}

// Initialize MorphCast module
let hraMorphCast = null;

// Listen for initialization message
window.addEventListener('message', (event) => {
    if (event.source === window && event.data && event.data.type === 'START_MORPHCAST') {
        if (!hraMorphCast) {
            hraMorphCast = new HRAMorphCastModule();
        }
        
        // Delay to ensure initialization
        setTimeout(() => {
            if (hraMorphCast.isInitialized) {
                hraMorphCast.startEmotionCapture();
            }
        }, 1000);
    }
});

// Auto-initialize if needed
document.addEventListener('DOMContentLoaded', () => {
    console.log('HRA MorphCast injection script loaded');
});
