// HRA Local AI Server - Interfaces with LM Studio
// Provides WebSocket server for Chrome Extension and HTTP API for AI processing

const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const schemas = require('./schemas');
const LLMManager = require('./llm-config');

class HRALocalAIServer {
    constructor() {
        this.port = 9876;
        this.lmStudioUrl = 'http://localhost:1234/v1/chat/completions'; // Legacy - will be replaced by LLMManager
        this.app = express();
        this.server = null;
        this.wss = null;
        this.llmManager = new LLMManager();
        
        // Data storage
        this.sessions = new Map();
        this.emotionHistory = [];
        this.textCaptures = [];
        this.behavioralData = [];
        
        // AI context and memory
        this.userContext = {
            emotionalProfile: {},
            interests: [],
            behaviorPatterns: {},
            sessionHistory: []
        };
        
        this.initialize();
    }

    initialize() {
        this.setupExpress();
        this.setupWebSocket();
        this.startServer();
    }

    setupExpress() {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.static('public'));

        // Health check endpoint
        this.app.get('/health', async (req, res) => {
            const lmStudioConnected = await this.checkLMStudioConnection();
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                lmStudioConnected,
                server: 'HRA Local AI Server',
                version: '1.0.0'
            });
        });

        // Simple test endpoint
        this.app.get('/test', (req, res) => {
            res.json({
                message: 'HRA Local AI Server is working!',
                timestamp: new Date().toISOString()
            });
        });

        // Dashboard endpoint
        this.app.get('/dashboard', (req, res) => {
            res.sendFile(__dirname + '/public/dashboard.html');
        });

        // Chat interface endpoint
        this.app.get('/chat', (req, res) => {
            res.sendFile(__dirname + '/chat.html');
        });

        // MorphCast test interface
        this.app.get('/morphcast', (req, res) => {
            res.sendFile(__dirname + '/morphcast-test.html');
        });

        this.app.get('/api/dashboard', (req, res) => {
            res.json({
                sessions: this.sessions.size,
                emotionHistory: this.emotionHistory.length,
                textCaptures: this.textCaptures.length,
                behavioralData: this.behavioralData.length,
                userContext: this.userContext
            });
        });

        // API endpoints for data retrieval
        this.app.get('/api/emotions', (req, res) => {
            res.json(this.emotionHistory.slice(-100)); // Last 100 emotions
        });

        this.app.get('/api/text-captures', (req, res) => {
            res.json(this.textCaptures.slice(-50)); // Last 50 text captures
        });

        this.app.get('/api/behavioral', (req, res) => {
            res.json(this.behavioralData.slice(-100)); // Last 100 behavioral events
        });

        // AI chat endpoint
        this.app.post('/api/chat', async (req, res) => {
            try {
                const { message, context } = req.body;
                const response = await this.llmManager.processRequest(message, context, 'contextualResponse');
                res.json({ response, timestamp: new Date().toISOString() });
            } catch (error) {
                console.error('Chat API error:', error);
                res.status(500).json({ error: 'Failed to process chat request' });
            }
        });

        // LLM management endpoints
        this.app.get('/api/llm/status', (req, res) => {
            res.json(this.llmManager.getStatus());
        });

        this.app.post('/api/llm/enable/:provider', (req, res) => {
            const { provider } = req.params;
            this.llmManager.enableProvider(provider);
            res.json({ message: `Provider ${provider} enabled` });
        });

        this.app.post('/api/llm/disable/:provider', (req, res) => {
            const { provider } = req.params;
            this.llmManager.disableProvider(provider);
            res.json({ message: `Provider ${provider} disabled` });
        });

        this.app.post('/api/llm/apikey/:provider', (req, res) => {
            const { provider } = req.params;
            const { apiKey } = req.body;
            this.llmManager.setApiKey(provider, apiKey);
            res.json({ message: `API key set for ${provider}` });
        });

        // Emotion analysis endpoint
        this.app.post('/api/analyze-emotion', async (req, res) => {
            try {
                const { emotionData, context } = req.body;
                const analysis = await this.analyzeEmotionWithAI(emotionData, context);
                res.json({ analysis, timestamp: new Date().toISOString() });
            } catch (error) {
                console.error('Emotion analysis API error:', error);
                res.status(500).json({ error: 'Failed to analyze emotion data' });
            }
        });

        // Behavioral analysis endpoint
        this.app.post('/api/analyze-behavior', async (req, res) => {
            try {
                const { behaviorData, context } = req.body;
                const analysis = await this.analyzeBehaviorWithAI(behaviorData, context);
                res.json({ analysis, timestamp: new Date().toISOString() });
            } catch (error) {
                console.error('Behavior analysis API error:', error);
                res.status(500).json({ error: 'Failed to analyze behavioral data' });
            }
        });
    }

    setupWebSocket() {
        // Will be initialized in startServer()
        console.log('WebSocket server will be attached to HTTP server');
    }

    async handleWebSocketMessage(sessionId, message) {
        const session = this.sessions.get(sessionId);
        if (!session) return;

        session.lastActivity = new Date();

        switch (message.type) {
            case 'handshake':
                await this.handleHandshake(sessionId, message);
                break;
            case 'capturedText':
                await this.handleTextCapture(sessionId, message.data);
                break;
            case 'emotionData':
                await this.handleEmotionData(sessionId, message.data);
                break;
            case 'behavioralData':
                await this.handleBehavioralData(sessionId, message.data);
                break;
            case 'behavioral_update':
                await this.handleBehavioralUpdate(sessionId, message.data);
                break;
            case 'activity_update':
                await this.handleActivityUpdate(sessionId, message.data);
                break;
            case 'bulk_context_sync':
                await this.handleBulkContextSync(sessionId, message.data);
                break;
            case 'module_status_update':
                this.handleModuleUpdate(sessionId, message);
                break;
            case 'context_request':
                await this.handleContextRequest(sessionId, message);
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    async handleHandshake(sessionId, message) {
        const session = this.sessions.get(sessionId);
        session.modules = message.modules || {};
        
        console.log(`Handshake completed for ${sessionId}:`, session.modules);
        
        session.ws.send(JSON.stringify({
            type: 'handshake_complete',
            sessionId,
            serverCapabilities: {
                aiProcessing: await this.checkLMStudioConnection(),
                emotionAnalysis: true,
                behavioralTracking: true,
                contextMemory: true
            }
        }));
    }

    async handleTextCapture(sessionId, data) {
        console.log(`Text captured from ${sessionId}:`, data.text?.substring(0, 100) + '...');
        
        // Store text capture
        const capture = {
            sessionId,
            timestamp: new Date().toISOString(),
            ...data
        };
        this.textCaptures.push(capture);
        
        // Process with AI if available
        if (await this.checkLMStudioConnection()) {
            try {
                const analysis = await this.analyzeTextWithAI(data.text, data.context);
                
                // Send analysis back to extension
                const session = this.sessions.get(sessionId);
                if (session) {
                    session.ws.send(JSON.stringify({
                        type: 'text_analysis',
                        data: analysis,
                        originalCapture: data
                    }));
                }
            } catch (error) {
                console.error('Text analysis error:', error);
            }
        }
    }

    async handleEmotionData(sessionId, data) {
        console.log(`Emotion data from ${sessionId}:`, data);
        
        // Store emotion data
        const emotion = {
            sessionId,
            timestamp: new Date().toISOString(),
            ...data
        };
        this.emotionHistory.push(emotion);
        
        // Update user's emotional profile
        this.updateEmotionalProfile(data);
        
        // Keep only last 1000 emotions
        if (this.emotionHistory.length > 1000) {
            this.emotionHistory = this.emotionHistory.slice(-1000);
        }
    }

    async handleBehavioralData(sessionId, data) {
        console.log(`Behavioral data from ${sessionId}:`, data.eventType);
        
        // Store behavioral data
        const behavioral = {
            sessionId,
            timestamp: new Date().toISOString(),
            ...data
        };
        this.behavioralData.push(behavioral);
        
        // Update behavior patterns
        this.updateBehaviorPatterns(data);
        
        // Keep only last 1000 behavioral events
        if (this.behavioralData.length > 1000) {
            this.behavioralData = this.behavioralData.slice(-1000);
        }
    }

    async handleBehavioralUpdate(sessionId, data) {
        console.log(`Behavioral update from ${sessionId}:`, data?.eventType || data?.entry?.type || 'general');

        // Handle different types of behavioral updates
        const behavioralEvent = {
            sessionId,
            timestamp: new Date().toISOString(),
            type: 'behavioral_update',
            ...data
        };

        this.behavioralData.push(behavioralEvent);

        // Update behavior patterns
        this.updateBehaviorPatterns(data);

        // Keep only last 1000 events
        if (this.behavioralData.length > 1000) {
            this.behavioralData = this.behavioralData.slice(-1000);
        }
    }

    async handleActivityUpdate(sessionId, data) {
        console.log(`Activity update from ${sessionId}`);

        const activityEvent = {
            sessionId,
            timestamp: new Date().toISOString(),
            type: 'activity_update',
            ...data
        };

        // Store activity data
        if (!this.activityData) {
            this.activityData = [];
        }
        this.activityData.push(activityEvent);

        // Keep only last 500 activity events
        if (this.activityData.length > 500) {
            this.activityData = this.activityData.slice(-500);
        }
    }

    async handleBulkContextSync(sessionId, data) {
        console.log(`Bulk context sync from ${sessionId}:`, Object.keys(data || {}).length, 'items');

        const session = this.sessions.get(sessionId);
        if (session && data) {
            // Update session context with bulk data
            session.context = {
                ...session.context,
                ...data,
                lastSync: new Date().toISOString()
            };

            // Process any text data for AI analysis
            if (data.textSelections && data.textSelections.length > 0) {
                for (const selection of data.textSelections.slice(-5)) { // Process last 5 selections
                    await this.analyzeTextWithAI(selection.text, session.context);
                }
            }
        }
    }

    handleModuleUpdate(sessionId, message) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.modules[message.module] = { enabled: message.enabled };
            console.log(`Module ${message.module} ${message.enabled ? 'enabled' : 'disabled'} for ${sessionId}`);
        }
    }

    async handleContextRequest(sessionId, message) {
        const context = await this.generateContextResponse(message.query);
        const session = this.sessions.get(sessionId);
        
        if (session) {
            session.ws.send(JSON.stringify({
                type: 'context_response',
                query: message.query,
                context,
                timestamp: new Date().toISOString()
            }));
        }
    }

    async checkLMStudioConnection() {
        try {
            const response = await axios.get('http://localhost:1234/v1/models', { timeout: 2000 });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    async processWithLMStudio(message, context = {}, schemaType = null) {
        if (!await this.checkLMStudioConnection()) {
            throw new Error('LM Studio not available');
        }

        const systemPrompt = this.buildSystemPrompt(context, schemaType);

        const requestBody = {
            model: "gemma-3-4b-it@q4_k_m", // Adjust model name as needed
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: message }
            ],
            temperature: 0.7,
            max_tokens: 1000,
            stream: false
        };

        // Try with JSON schema first, fallback to regular request if it fails
        if (schemaType && schemas[schemaType]) {
            try {
                requestBody.response_format = schemas[schemaType];
                const response = await axios.post(this.lmStudioUrl, requestBody, { timeout: 30000 });

                // Parse JSON response
                try {
                    return JSON.parse(response.data.choices[0].message.content);
                } catch (parseError) {
                    console.warn('Failed to parse structured JSON response, falling back to regular request');
                    // Fall through to regular request
                }
            } catch (error) {
                console.warn('Structured output failed, falling back to regular request:', error.message);
                // Remove response_format and try again
                delete requestBody.response_format;
            }
        }

        // Regular request without structured output
        try {
            const response = await axios.post(this.lmStudioUrl, requestBody, { timeout: 30000 });
            return response.data.choices[0].message.content;
        } catch (error) {
            console.error('LM Studio request failed:', error.message);
            throw new Error(`LM Studio request failed: ${error.message}`);
        }
    }

    buildSystemPrompt(context, schemaType = null) {
        let basePrompt = `You are HRA (Harmonic Resonance Agent), an empathetic AI companion that understands the user's emotional state and behavior patterns.

Current Context:
- Recent emotions: ${JSON.stringify(this.userContext.emotionalProfile)}
- Behavior patterns: ${JSON.stringify(this.userContext.behaviorPatterns)}
- Session context: ${JSON.stringify(context)}`;

        // Add schema-specific instructions
        if (schemaType) {
            const schemaInstructions = {
                emotionAnalysis: "Analyze the emotional content and provide a structured emotion analysis. Focus on identifying primary emotions, confidence levels, and actionable recommendations.",
                textAnalysis: "Analyze the text content for sentiment, themes, and key concepts. Provide a comprehensive analysis including emotional tone and complexity level.",
                behaviorAnalysis: "Analyze behavioral patterns and engagement levels. Provide insights into attention patterns, interaction styles, and recommendations for improvement.",
                contextualResponse: "Provide an empathetic response that takes into account the user's emotional state. Include emotional awareness and suggested actions.",
                profileUpdate: "Update the user profile based on recent interactions. Analyze personality traits, interests, and behavioral preferences."
            };

            basePrompt += `\n\nTask: ${schemaInstructions[schemaType] || 'Provide a structured response according to the specified schema.'}`;
            basePrompt += `\n\nIMPORTANT: Respond ONLY with valid JSON that matches the required schema. Do not include any text outside the JSON structure.`;
        } else {
            basePrompt += `\n\nRespond with empathy and understanding, taking into account the user's emotional state and recent activities.`;
        }

        return basePrompt;
    }

    async analyzeTextWithAI(text, context) {
        try {
            const analysis = await this.llmManager.processRequest(
                `Analyze this text for emotional content, themes, and significance: "${text}"`,
                context,
                'textAnalysis'
            );

            // If structured analysis succeeded, return it
            if (typeof analysis === 'object' && analysis.sentiment) {
                return {
                    ...analysis,
                    timestamp: new Date().toISOString()
                };
            }

            // Fallback to simple analysis
            return {
                analysis: typeof analysis === 'string' ? analysis : JSON.stringify(analysis),
                sentiment: this.extractSentiment(text),
                themes: this.extractThemes(text),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('AI analysis error:', error);
            return null;
        }
    }

    updateEmotionalProfile(emotionData) {
        // Simple emotion aggregation - can be made more sophisticated
        if (emotionData.emotions) {
            Object.keys(emotionData.emotions).forEach(emotion => {
                if (!this.userContext.emotionalProfile[emotion]) {
                    this.userContext.emotionalProfile[emotion] = [];
                }
                this.userContext.emotionalProfile[emotion].push({
                    value: emotionData.emotions[emotion],
                    timestamp: new Date().toISOString()
                });
                
                // Keep only last 50 readings per emotion
                if (this.userContext.emotionalProfile[emotion].length > 50) {
                    this.userContext.emotionalProfile[emotion] = 
                        this.userContext.emotionalProfile[emotion].slice(-50);
                }
            });
        }
    }

    updateBehaviorPatterns(behavioralData) {
        const eventType = behavioralData.eventType;
        if (!this.userContext.behaviorPatterns[eventType]) {
            this.userContext.behaviorPatterns[eventType] = {
                count: 0,
                lastSeen: null,
                patterns: []
            };
        }
        
        this.userContext.behaviorPatterns[eventType].count++;
        this.userContext.behaviorPatterns[eventType].lastSeen = new Date().toISOString();
        this.userContext.behaviorPatterns[eventType].patterns.push({
            data: behavioralData,
            timestamp: new Date().toISOString()
        });
        
        // Keep only last 20 patterns per event type
        if (this.userContext.behaviorPatterns[eventType].patterns.length > 20) {
            this.userContext.behaviorPatterns[eventType].patterns = 
                this.userContext.behaviorPatterns[eventType].patterns.slice(-20);
        }
    }

    extractSentiment(text) {
        // Simple sentiment analysis - can be enhanced
        const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic'];
        const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];
        
        const words = text.toLowerCase().split(/\s+/);
        let score = 0;
        
        words.forEach(word => {
            if (positiveWords.includes(word)) score += 1;
            if (negativeWords.includes(word)) score -= 1;
        });
        
        return score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral';
    }

    extractThemes(text) {
        // Simple theme extraction - can be enhanced with NLP
        const themes = [];
        const themeKeywords = {
            technology: ['tech', 'software', 'computer', 'digital', 'ai', 'programming'],
            emotion: ['feel', 'emotion', 'happy', 'sad', 'angry', 'excited'],
            work: ['work', 'job', 'career', 'office', 'meeting', 'project'],
            personal: ['family', 'friend', 'relationship', 'personal', 'life']
        };
        
        const words = text.toLowerCase().split(/\s+/);
        
        Object.keys(themeKeywords).forEach(theme => {
            const matches = words.filter(word => 
                themeKeywords[theme].some(keyword => word.includes(keyword))
            );
            if (matches.length > 0) {
                themes.push({ theme, relevance: matches.length / words.length });
            }
        });
        
        return themes.sort((a, b) => b.relevance - a.relevance);
    }

    async generateContextResponse(query) {
        // Generate contextual response based on stored data
        const recentEmotions = this.emotionHistory.slice(-10);
        const recentTexts = this.textCaptures.slice(-5);
        const recentBehavior = this.behavioralData.slice(-10);
        
        return {
            query,
            recentEmotions,
            recentTexts: recentTexts.map(t => ({ text: t.text?.substring(0, 100), context: t.context })),
            recentBehavior: recentBehavior.map(b => ({ eventType: b.eventType, timestamp: b.timestamp })),
            userProfile: this.userContext
        };
    }

    startServer() {
        this.server = this.app.listen(this.port, () => {
            console.log(`🚀 HRA Local AI Server running on port ${this.port}`);
            console.log(`📊 Dashboard: http://localhost:${this.port}/dashboard`);
            console.log(`🔌 WebSocket: ws://localhost:${this.port}`);

            // Check LM Studio connection
            this.checkLMStudioConnection().then(connected => {
                console.log(`🤖 LM Studio: ${connected ? 'Connected' : 'Disconnected'}`);
            });
        });

        // Create WebSocket server attached to HTTP server
        this.wss = new WebSocket.Server({ server: this.server });
        this.setupWebSocketHandlers();
    }

    setupWebSocketHandlers() {
        console.log('Setting up WebSocket handlers on server');
        
        this.wss.on('connection', (ws, req) => {
            const sessionId = uuidv4();
            const clientIp = req.socket.remoteAddress;
            
            console.log(`New WebSocket connection: ${sessionId} from ${clientIp}`);
            console.log('Connection headers:', req.headers);

            // Store session
            this.sessions.set(sessionId, {
                ws,
                id: sessionId,
                connected: new Date(),
                lastActivity: new Date(),
                modules: {}
            });

            ws.on('message', async (data) => {
                console.log(`Received WebSocket message from ${sessionId}:`, data.toString().substring(0, 100));
                try {
                    const message = JSON.parse(data);
                    await this.handleWebSocketMessage(sessionId, message);
                } catch (error) {
                    console.error('WebSocket message error:', error);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: 'Invalid message format'
                    }));
                }
            });

            ws.on('error', (error) => {
                console.error(`WebSocket error for ${sessionId}:`, error);
            });
            
            ws.on('close', (code, reason) => {
                console.log(`WebSocket disconnected: ${sessionId}, code: ${code}, reason: ${reason}`);
                this.sessions.delete(sessionId);
            });

            // Send welcome message
            ws.send(JSON.stringify({
                type: 'welcome',
                sessionId,
                timestamp: new Date().toISOString()
            }));
        });
        
        this.wss.on('error', (error) => {
            console.error('WebSocket server error:', error);
        });
        
        console.log('WebSocket server handlers initialized');
    }
}

// Start the server
const server = new HRALocalAIServer();

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down HRA Local AI Server...');
    if (server.server) {
        server.server.close(() => {
            console.log('✅ Server closed');
            process.exit(0);
        });
    }
});
