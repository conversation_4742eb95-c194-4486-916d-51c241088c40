{"name": "hra-local-ai-server", "version": "1.0.0", "description": "Local AI Server for Harmonic Resonance Agent using LM Studio", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "websocket", "lm-studio", "chrome-extension", "emotion-analysis"], "author": "<PERSON> <gabe<PERSON><PERSON><PERSON><PERSON>@mac.com>", "license": "MIT", "dependencies": {"ws": "^8.18.0", "express": "^4.21.2", "cors": "^2.8.5", "axios": "^1.7.9", "uuid": "^11.0.4"}, "devDependencies": {"nodemon": "^3.1.9"}}