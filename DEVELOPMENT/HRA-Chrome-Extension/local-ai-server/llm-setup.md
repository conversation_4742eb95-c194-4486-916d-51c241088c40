# 🤖 Multi-LLM Setup Guide for HRA Local AI Server

The HRA Local AI Server now supports multiple LLM providers with automatic fallback. Here's how to connect different LLMs:

## 🔧 **Currently Supported LLMs**

### 1. **LM Studio** (Default - Already Connected)
- **Status**: ✅ Active
- **Port**: 1234
- **Models**: Gemma, Llama, Mistral, etc.
- **Structured Output**: ✅ Yes
- **Setup**: Already configured!

### 2. **Ollama** 
- **Port**: 11434
- **Models**: Llama3.2, Mistral, CodeLlama
- **Structured Output**: ❌ No
- **Setup**:
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama3.2

# Start Ollama (runs on port 11434 by default)
ollama serve
```

### 3. **OpenAI API**
- **Models**: GPT-4, GPT-3.5-turbo
- **Structured Output**: ✅ Yes
- **Setup**: Requires API key
```bash
export OPENAI_API_KEY="your-api-key-here"
```

### 4. **Anthropic Claude**
- **Models**: Claude-3-Sonnet, Claude-3-Haiku
- **Structured Output**: ❌ No
- **Setup**: Requires API key
```bash
export ANTHROPIC_API_KEY="your-api-key-here"
```

### 5. **LocalAI**
- **Port**: 8080
- **Models**: Various local models
- **Structured Output**: ❌ No
- **Setup**:
```bash
# Docker setup
docker run -p 8080:8080 --name local-ai -ti localai/localai:latest
```

## 🚀 **Quick Setup Examples**

### **Enable Ollama**
```bash
# 1. Start Ollama
ollama serve

# 2. Pull a model
ollama pull llama3.2

# 3. Enable in HRA Server
curl -X POST http://localhost:9876/api/llm/enable/ollama
```

### **Enable OpenAI**
```bash
# 1. Set API key
curl -X POST http://localhost:9876/api/llm/apikey/openai \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "your-openai-api-key"}'

# 2. Enable provider
curl -X POST http://localhost:9876/api/llm/enable/openai
```

### **Check Status**
```bash
curl http://localhost:9876/api/llm/status
```

## 📋 **API Endpoints for LLM Management**

### **Get LLM Status**
```http
GET /api/llm/status
```
Returns current active provider and all available providers.

### **Enable Provider**
```http
POST /api/llm/enable/{provider}
```
Enables a specific LLM provider (lmstudio, ollama, openai, anthropic, localai).

### **Disable Provider**
```http
POST /api/llm/disable/{provider}
```
Disables a specific LLM provider.

### **Set API Key**
```http
POST /api/llm/apikey/{provider}
Content-Type: application/json

{
  "apiKey": "your-api-key-here"
}
```

## 🔄 **Automatic Fallback System**

The server automatically tries providers in this order:
1. **LM Studio** (Priority 1)
2. **Ollama** (Priority 2) 
3. **OpenAI** (Priority 3)
4. **Anthropic** (Priority 4)
5. **LocalAI** (Priority 5)

If one fails, it automatically tries the next available provider.

## 🛠 **Configuration Examples**

### **Environment Variables**
Create a `.env` file:
```env
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
```

### **Custom Provider Setup**
```javascript
// Add to llm-config.js
customProvider: {
    name: 'Custom LLM',
    baseUrl: 'http://localhost:8000/v1',
    models: ['custom-model'],
    supportsStructuredOutput: false,
    priority: 6,
    enabled: false
}
```

## 🧪 **Testing Different LLMs**

### **Test with LM Studio (Gemma)**
```bash
curl -X POST http://localhost:9876/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello! How are you?",
    "context": {"source": "test"}
  }'
```

### **Test with Ollama (Llama)**
```bash
# First enable Ollama
curl -X POST http://localhost:9876/api/llm/enable/ollama

# Then test
curl -X POST http://localhost:9876/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Explain quantum computing",
    "context": {"source": "test"}
  }'
```

## 📊 **Monitoring**

Check the HRA Dashboard at http://localhost:9876/dashboard to see:
- Active LLM provider
- Fallback chain status
- Request success/failure rates
- Response times per provider

## 🔧 **Troubleshooting**

### **Provider Not Available**
1. Check if the service is running on the correct port
2. Verify API keys are set correctly
3. Check firewall/network settings
4. Look at server logs for specific error messages

### **Structured Output Issues**
- Only LM Studio and OpenAI support structured JSON output
- Other providers will return plain text responses
- The server handles this automatically

### **Performance Optimization**
- Local providers (LM Studio, Ollama) are fastest
- API providers (OpenAI, Anthropic) have network latency
- Adjust priority order based on your needs

## 🎯 **Best Practices**

1. **Keep LM Studio as primary** for best performance and structured output
2. **Use Ollama as backup** for local processing
3. **Use OpenAI for complex tasks** that need advanced reasoning
4. **Monitor usage** to avoid API rate limits
5. **Set appropriate timeouts** for each provider

## 🔄 **Dynamic Switching**

The system can switch providers mid-conversation if needed:
- If primary provider fails, automatically uses backup
- Maintains conversation context across providers
- Logs all provider switches for debugging

This multi-LLM setup ensures your HRA Chrome Extension always has AI processing available, even if one service goes down! 🚀
