// J<PERSON><PERSON>hemas for LM Studio API responses
// Ensures consistent, structured outputs from Gemma model

const schemas = {
    // Schema for emotion analysis responses
    emotionAnalysis: {
        type: "object",
        properties: {
            primary_emotion: {
                type: "string",
                enum: ["joy", "sadness", "anger", "fear", "surprise", "disgust", "neutral", "excitement", "anxiety", "contentment"]
            },
            confidence: {
                type: "number",
                minimum: 0,
                maximum: 1,
                description: "Confidence level of the emotion detection"
            },
            emotional_intensity: {
                type: "number",
                minimum: 0,
                maximum: 10,
                description: "Intensity of the detected emotion on a scale of 0-10"
            },
            secondary_emotions: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        emotion: {
                            type: "string",
                            enum: ["joy", "sadness", "anger", "fear", "surprise", "disgust", "neutral", "excitement", "anxiety", "contentment"]
                        },
                        confidence: {
                            type: "number",
                            minimum: 0,
                            maximum: 1
                        }
                    },
                    required: ["emotion", "confidence"]
                },
                maxItems: 3
            },
            emotional_context: {
                type: "string",
                description: "Brief explanation of the emotional context"
            },
            recommendations: {
                type: "array",
                items: {
                    type: "string"
                },
                maxItems: 3,
                description: "Actionable recommendations based on emotional state"
            }
        },
        required: ["primary_emotion", "confidence", "emotional_intensity", "emotional_context"]
    },

    // Schema for text content analysis
    textAnalysis: {
        type: "object",
        properties: {
            sentiment: {
                type: "string",
                enum: ["very_positive", "positive", "neutral", "negative", "very_negative"]
            },
            sentiment_score: {
                type: "number",
                minimum: -1,
                maximum: 1,
                description: "Sentiment score from -1 (very negative) to 1 (very positive)"
            },
            themes: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        theme: {
                            type: "string",
                            enum: ["technology", "work", "personal", "health", "education", "entertainment", "news", "social", "finance", "travel"]
                        },
                        relevance: {
                            type: "number",
                            minimum: 0,
                            maximum: 1
                        },
                        keywords: {
                            type: "array",
                            items: {
                                type: "string"
                            },
                            maxItems: 5
                        }
                    },
                    required: ["theme", "relevance"]
                },
                maxItems: 5
            },
            key_concepts: {
                type: "array",
                items: {
                    type: "string"
                },
                maxItems: 10,
                description: "Important concepts or entities mentioned in the text"
            },
            emotional_tone: {
                type: "string",
                enum: ["enthusiastic", "calm", "frustrated", "curious", "concerned", "excited", "analytical", "casual"]
            },
            complexity_level: {
                type: "string",
                enum: ["simple", "moderate", "complex", "technical"]
            },
            summary: {
                type: "string",
                maxLength: 200,
                description: "Brief summary of the text content"
            }
        },
        required: ["sentiment", "sentiment_score", "themes", "emotional_tone", "summary"]
    },

    // Schema for behavioral pattern analysis
    behaviorAnalysis: {
        type: "object",
        properties: {
            engagement_level: {
                type: "string",
                enum: ["very_low", "low", "moderate", "high", "very_high"]
            },
            attention_pattern: {
                type: "string",
                enum: ["focused", "scattered", "browsing", "searching", "deep_reading"]
            },
            interaction_style: {
                type: "string",
                enum: ["passive", "selective", "active", "intensive"]
            },
            session_quality: {
                type: "number",
                minimum: 0,
                maximum: 10,
                description: "Overall quality of the browsing session"
            },
            behavioral_insights: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        insight: {
                            type: "string",
                            description: "Behavioral insight or pattern observed"
                        },
                        confidence: {
                            type: "number",
                            minimum: 0,
                            maximum: 1
                        },
                        category: {
                            type: "string",
                            enum: ["attention", "engagement", "preference", "habit", "mood"]
                        }
                    },
                    required: ["insight", "confidence", "category"]
                },
                maxItems: 5
            },
            recommendations: {
                type: "array",
                items: {
                    type: "string"
                },
                maxItems: 3,
                description: "Recommendations to improve user experience or wellbeing"
            }
        },
        required: ["engagement_level", "attention_pattern", "interaction_style", "session_quality"]
    },

    // Schema for contextual AI responses
    contextualResponse: {
        type: "object",
        properties: {
            response: {
                type: "string",
                description: "The main AI response to the user"
            },
            emotional_awareness: {
                type: "object",
                properties: {
                    detected_user_state: {
                        type: "string",
                        enum: ["calm", "stressed", "excited", "focused", "distracted", "curious", "frustrated"]
                    },
                    empathy_level: {
                        type: "number",
                        minimum: 0,
                        maximum: 10,
                        description: "Level of empathy to show in response"
                    },
                    tone_adjustment: {
                        type: "string",
                        enum: ["supportive", "encouraging", "informative", "casual", "professional"]
                    }
                },
                required: ["detected_user_state", "empathy_level", "tone_adjustment"]
            },
            suggested_actions: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        action: {
                            type: "string",
                            description: "Suggested action for the user"
                        },
                        priority: {
                            type: "string",
                            enum: ["low", "medium", "high"]
                        },
                        category: {
                            type: "string",
                            enum: ["wellbeing", "productivity", "learning", "social", "health"]
                        }
                    },
                    required: ["action", "priority", "category"]
                },
                maxItems: 3
            },
            follow_up_questions: {
                type: "array",
                items: {
                    type: "string"
                },
                maxItems: 2,
                description: "Questions to continue the conversation"
            },
            confidence: {
                type: "number",
                minimum: 0,
                maximum: 1,
                description: "AI's confidence in the response appropriateness"
            }
        },
        required: ["response", "emotional_awareness", "confidence"]
    },

    // Schema for user profile updates
    profileUpdate: {
        type: "object",
        properties: {
            personality_traits: {
                type: "object",
                properties: {
                    openness: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    conscientiousness: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    extraversion: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    agreeableness: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    neuroticism: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    }
                }
            },
            interests: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        category: {
                            type: "string",
                            enum: ["technology", "science", "arts", "sports", "music", "literature", "travel", "food", "health", "business"]
                        },
                        strength: {
                            type: "number",
                            minimum: 0,
                            maximum: 10
                        },
                        recent_activity: {
                            type: "boolean"
                        }
                    },
                    required: ["category", "strength"]
                },
                maxItems: 10
            },
            emotional_patterns: {
                type: "object",
                properties: {
                    dominant_emotions: {
                        type: "array",
                        items: {
                            type: "string",
                            enum: ["joy", "sadness", "anger", "fear", "surprise", "disgust", "neutral", "excitement", "anxiety", "contentment"]
                        },
                        maxItems: 3
                    },
                    emotional_stability: {
                        type: "number",
                        minimum: 0,
                        maximum: 10
                    },
                    stress_indicators: {
                        type: "array",
                        items: {
                            type: "string"
                        },
                        maxItems: 5
                    }
                }
            },
            behavioral_preferences: {
                type: "object",
                properties: {
                    preferred_interaction_style: {
                        type: "string",
                        enum: ["direct", "gentle", "detailed", "concise", "encouraging"]
                    },
                    optimal_session_length: {
                        type: "number",
                        minimum: 5,
                        maximum: 180,
                        description: "Preferred session length in minutes"
                    },
                    attention_span: {
                        type: "string",
                        enum: ["short", "medium", "long", "variable"]
                    }
                }
            },
            update_confidence: {
                type: "number",
                minimum: 0,
                maximum: 1,
                description: "Confidence in the profile update accuracy"
            }
        },
        required: ["update_confidence"]
    }
};

module.exports = schemas;
