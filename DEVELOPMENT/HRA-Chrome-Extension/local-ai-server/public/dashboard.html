<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRA Local AI Server Dashboard</title>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #48bb78;
            --warning-color: #f6ad55;
            --error-color: #f56565;
            --bg-color: #f7fafc;
            --text-color: #2d3748;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .status-connected {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .status-disconnected {
            background: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
        }

        .metric-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .api-endpoints {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .endpoint {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 1rem;
            background: var(--bg-color);
        }

        .endpoint:last-child {
            margin-bottom: 0;
        }

        .method {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .method-get {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
        }

        .method-post {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }

        .endpoint-path {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            flex: 1;
        }

        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .refresh-btn:hover {
            background: var(--secondary-color);
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 HRA Local AI Server</h1>
            <p>Harmonic Resonance Agent - Dashboard</p>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <div class="card-title">Server Status</div>
                <div id="serverStatus" class="status-indicator status-connected">
                    <div class="status-dot"></div>
                    Server Running
                </div>
                <div class="metric">
                    <span class="metric-label">Uptime</span>
                    <span class="metric-value" id="uptime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Port</span>
                    <span class="metric-value">9876</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Version</span>
                    <span class="metric-value">1.0.0</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">LM Studio Connection</div>
                <div id="lmStudioStatus" class="status-indicator">
                    <div class="status-dot"></div>
                    <span id="lmStudioText">Checking...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Endpoint</span>
                    <span class="metric-value">localhost:1234</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Model</span>
                    <span class="metric-value">gemma-3-4b-it@q4_k_m</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Structured Output</span>
                    <span class="metric-value">Enabled</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">Session Statistics</div>
                <div class="metric">
                    <span class="metric-label">Active Sessions</span>
                    <span class="metric-value" id="activeSessions">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Emotions</span>
                    <span class="metric-value" id="totalEmotions">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Text Captures</span>
                    <span class="metric-value" id="textCaptures">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Behavioral Events</span>
                    <span class="metric-value" id="behavioralEvents">0</span>
                </div>
            </div>

            <div class="card">
                <div class="card-title">Chrome Extension</div>
                <div class="metric">
                    <span class="metric-label">WebSocket Port</span>
                    <span class="metric-value">9876</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Extension Status</span>
                    <span class="metric-value" id="extensionStatus">Ready to Connect</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Build Status</span>
                    <span class="metric-value">✅ Built</span>
                </div>
                <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
            </div>
        </div>

        <div class="api-endpoints">
            <div class="card-title">API Endpoints</div>
            <div class="endpoint">
                <span class="method method-get">GET</span>
                <span class="endpoint-path">/health</span>
                <span>Health check and status</span>
            </div>
            <div class="endpoint">
                <span class="method method-get">GET</span>
                <span class="endpoint-path">/test</span>
                <span>Simple test endpoint</span>
            </div>
            <div class="endpoint">
                <span class="method method-post">POST</span>
                <span class="endpoint-path">/api/chat</span>
                <span>AI chat with structured output</span>
            </div>
            <div class="endpoint">
                <span class="method method-get">GET</span>
                <span class="endpoint-path">/api/dashboard</span>
                <span>Dashboard data (JSON)</span>
            </div>
            <div class="endpoint">
                <span class="method method-get">GET</span>
                <span class="endpoint-path">/api/emotions</span>
                <span>Recent emotion data</span>
            </div>
        </div>

        <div class="footer">
            <p>HRA Local AI Server v1.0.0 | Built with LM Studio Integration</p>
        </div>
    </div>

    <script>
        const startTime = Date.now();

        async function refreshData() {
            try {
                // Update uptime
                const uptime = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(uptime / 60);
                const seconds = uptime % 60;
                document.getElementById('uptime').textContent = `${minutes}m ${seconds}s`;

                // Check health
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();
                
                // Update LM Studio status
                const lmStudioStatus = document.getElementById('lmStudioStatus');
                const lmStudioText = document.getElementById('lmStudioText');
                
                if (healthData.lmStudioConnected) {
                    lmStudioStatus.className = 'status-indicator status-connected';
                    lmStudioText.textContent = 'Connected';
                } else {
                    lmStudioStatus.className = 'status-indicator status-disconnected';
                    lmStudioText.textContent = 'Disconnected';
                }

                // Get dashboard data
                const dashboardResponse = await fetch('/api/dashboard');
                const dashboardData = await dashboardResponse.json();
                
                document.getElementById('activeSessions').textContent = dashboardData.sessions;
                document.getElementById('totalEmotions').textContent = dashboardData.emotionHistory;
                document.getElementById('textCaptures').textContent = dashboardData.textCaptures;
                document.getElementById('behavioralEvents').textContent = dashboardData.behavioralData;
                
            } catch (error) {
                console.error('Failed to refresh data:', error);
            }
        }

        // Initial load and periodic refresh
        refreshData();
        setInterval(refreshData, 5000); // Refresh every 5 seconds
    </script>
</body>
</html>
