// Script to generate extension icons
const fs = require('fs');
const path = require('path');

// SVG icon template
const iconSVG = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="10" fill="url(#grad1)" stroke="#fff" stroke-width="1"/>
  <circle cx="12" cy="8" r="3" fill="#fff" opacity="0.9"/>
  <path d="M8 14c0-2.2 1.8-4 4-4s4 1.8 4 4" stroke="#fff" stroke-width="2" fill="none" opacity="0.9"/>
  <circle cx="9" cy="9" r="1" fill="#667eea"/>
  <circle cx="15" cy="9" r="1" fill="#667eea"/>
</svg>
`;

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '..', 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate SVG icons
const sizes = [16, 48, 128];
sizes.forEach(size => {
    const svgContent = iconSVG(size);
    fs.writeFileSync(path.join(iconsDir, `icon${size}.svg`), svgContent);
    console.log(`Created icon${size}.svg`);
});

console.log('Icons created successfully!');
console.log('Note: For production, convert SVG files to PNG using an online converter or tool like Inkscape.');
