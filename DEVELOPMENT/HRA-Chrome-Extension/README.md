# 🎭 Harmonic Resonance Agent (HRA)

**Real-time AI companion with emotion detection, behavioral analysis, and multi-LLM integration**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Chrome Extension](https://img.shields.io/badge/Chrome-Extension-blue.svg)](https://developer.chrome.com/docs/extensions/)
[![Real-time](https://img.shields.io/badge/Real--time-Streaming-red.svg)](https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API)

## 🌟 Overview

HRA is a comprehensive AI companion system that provides real-time emotional intelligence and behavioral analysis. It combines advanced emotion detection, behavioral tracking, and multi-LLM integration to create truly context-aware AI interactions.

### ✨ Key Features

- **🎭 Real-time Emotion Detection** - Advanced facial emotion analysis via MorphCast
- **📊 Behavioral Analytics** - Comprehensive user behavior tracking and pattern analysis
- **🤖 Multi-LLM Integration** - Support for multiple AI providers with automatic fallback
- **🔄 Real-time Streaming** - Continuous context updates to connected AI systems
- **🧠 Memory Integration** - Long-term learning and personalization via Mem0
- **🌐 Beautiful Interfaces** - Modern web dashboard and chat interfaces
- **🔌 Chrome Extension** - Seamless browser integration for continuous monitoring

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm
- **Chrome Browser** for extension
- **LM Studio** (recommended) or other LLM provider

### 1. Clone and Setup

```bash
git clone https://github.com/your-org/hra-chrome-extension.git
cd hra-chrome-extension

# Install dependencies
cd local-ai-server
npm install
```

### 2. Start the HRA Server

```bash
cd local-ai-server
node server.js
```

The server will start on `http://localhost:9876` with:
- 📊 **Dashboard**: http://localhost:9876/dashboard
- 💬 **Chat Interface**: http://localhost:9876/chat
- 🎭 **MorphCast Test**: http://localhost:9876/morphcast

### 3. Install Chrome Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `dist` folder
4. The HRA extension icon should appear in your toolbar

### 4. Connect Your LLM

```bash
# Subscribe your LLM to real-time updates
curl -X POST http://localhost:9876/api/llm/subscribe \
  -H "Content-Type: application/json" \
  -d '{
    "callbackUrl": "http://your-llm-server:5000/hra-context",
    "events": ["emotion", "behavior", "summary"]
  }'
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Chrome         │    │  HRA Local       │    │  LLM            │
│  Extension      │◄──►│  AI Server       │◄──►│  Integration    │
│                 │    │                  │    │                 │
│ • Emotion       │    │ • Real-time      │    │ • Context-aware │
│ • Behavior      │    │   Streaming      │    │   Responses     │
│ • Text Capture  │    │ • Multi-LLM      │    │ • Personalized  │
│ • Activity      │    │ • Memory         │    │   Interactions  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌──────────────────┐             │
         └──────────────►│  Mem0 Memory     │◄────────────┘
                        │  Integration     │
                        │                  │
                        │ • Long-term      │
                        │   Learning       │
                        │ • Pattern        │
                        │   Recognition    │
                        └──────────────────┘
```

## 📊 Data Streams

### Real-time Streaming Architecture

HRA provides continuous data streams that LLMs can subscribe to:

#### **Emotion Stream** (Every 5 seconds)
- Primary emotions: joy, sadness, anger, fear, surprise, disgust
- Advanced metrics: arousal, valence, attention, positivity
- Demographics: age, gender estimation
- Trends: emotional stability, intensity changes

#### **Behavior Stream** (Every 15 seconds)
- Activity patterns: clicks, scrolls, navigation
- Engagement metrics: activity level, focus duration
- Text interactions: selections, copies, form inputs
- Temporal patterns: usage frequency, session duration

#### **Context Summary** (Every 10 seconds)
- User profile: interests, preferences, emotional profile
- Session statistics: active time, interaction count
- Real-time metrics: current engagement, attention level
- Pattern analysis: behavioral trends, emotional stability

## 🤖 LLM Integration

### Supported Providers

- **LM Studio** (Primary) - Local model hosting
- **OpenAI GPT** - Cloud-based AI
- **Anthropic Claude** - Advanced reasoning
- **Custom Models** - Bring your own LLM

### Integration Examples

**Python Flask:**
```python
from flask import Flask, request, jsonify
import requests

app = Flask(__name__)

@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    update = request.json
    
    if update['category'] == 'emotion':
        emotion_data = update['data']
        if emotion_data.get('current', {}).get('joy', 0) > 0.7:
            print("User is happy! 😊")
    
    return jsonify({"status": "received"})

# Get current context
context = requests.get("http://localhost:9876/api/stream/context").json()
```

**Node.js Express:**
```javascript
const express = require('express');
const axios = require('axios');

app.post('/hra-context', (req, res) => {
    const update = req.body;
    
    if (update.category === 'behavior') {
        const engagement = update.data.engagement;
        if (engagement > 80) {
            console.log('High engagement! 🔥');
        }
    }
    
    res.json({ status: 'received' });
});
```

## 🎭 MorphCast Emotion Detection

### Comprehensive Emotion Analysis

- **12 Detection Modules** with optimized smoothness parameters
- **Real-time Processing** with 15-minute auto-stop
- **Advanced Metrics** including arousal/valence analysis
- **Attention Monitoring** with low-attention alerts
- **Demographics** age and gender estimation

### Configuration

```javascript
const morphcastConfig = {
    licenseKey: "your-morphcast-license",
    modules: {
        emotions: { smoothness: 0.40 },
        arousal_valence: { smoothness: 0.70 },
        attention: { smoothness: 0.83 },
        age_gender: { enabled: true },
        positivity: { smoothness: 0.40, gain: 2 }
    },
    autoStop: 900000 // 15 minutes
};
```

## 🧠 Memory Integration

### Mem0 Integration

HRA integrates with Mem0 for long-term memory and learning:

```python
# Store emotional patterns
await mem0.add_memory({
    content: "User prefers detailed explanations when in positive mood",
    userId: "hra-user"
});

# Retrieve relevant memories
memories = await mem0.search_memories({
    query: "user interaction preferences",
    userId: "hra-user"
});
```

## 📚 Documentation

### For Developers
- **[Quick Start Guide](./docs/QUICK_START.md)** - Get up and running in 5 minutes
- **[API Reference](./docs/API_REFERENCE.md)** - Complete API documentation
- **[LLM Integration Guide](./docs/LLM_INTEGRATION_GUIDE.md)** - Comprehensive integration examples

### For Users
- **[User Guide](./docs/USER_GUIDE.md)** - How to use the HRA system
- **[Privacy Policy](./docs/PRIVACY.md)** - Data handling and privacy information
- **[FAQ](./docs/FAQ.md)** - Frequently asked questions

## 🔧 Configuration

### Environment Variables

```bash
# Server Configuration
HRA_PORT=9876
HRA_HOST=localhost

# LLM Integration
LM_STUDIO_URL=http://localhost:1234
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# MorphCast
MORPHCAST_LICENSE_KEY=your-morphcast-license

# Streaming Configuration
EMOTION_STREAM_INTERVAL=5000    # 5 seconds
BEHAVIOR_STREAM_INTERVAL=15000  # 15 seconds
CONTEXT_STREAM_INTERVAL=10000   # 10 seconds
```

### Chrome Extension Settings

Access extension settings by clicking the HRA icon in your Chrome toolbar:

- **Emotion Detection** - Toggle MorphCast emotion analysis
- **Text Capture** - Enable/disable text selection tracking
- **Behavioral Tracking** - Control activity monitoring
- **AI Integration** - Configure LLM connections

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 9876
CMD ["node", "server.js"]
```

```bash
# Build and run
docker build -t hra-server .
docker run -p 9876:9876 hra-server
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hra-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hra-server
  template:
    metadata:
      labels:
        app: hra-server
    spec:
      containers:
      - name: hra-server
        image: hra-server:latest
        ports:
        - containerPort: 9876
        env:
        - name: NODE_ENV
          value: "production"
```

## 🔒 Security & Privacy

### Data Protection
- **Local Processing** - All emotion detection happens locally
- **Encrypted Communication** - WebSocket and HTTP traffic encryption
- **No Data Storage** - Real-time processing without persistent storage
- **User Control** - Complete control over data sharing and processing

### Security Features
- **API Authentication** - Secure API access with tokens
- **Rate Limiting** - Protection against abuse
- **Webhook Verification** - Signed webhook payloads
- **CORS Protection** - Controlled cross-origin access

## 📈 Monitoring & Analytics

### Built-in Dashboard

Access the dashboard at `http://localhost:9876/dashboard` to monitor:

- **Real-time Sessions** - Active users and connections
- **Emotion Analytics** - Emotional state trends and patterns
- **Behavioral Insights** - User engagement and activity metrics
- **System Health** - Server status and performance metrics

### API Endpoints

```bash
# System health
curl http://localhost:9876/health

# Current context
curl http://localhost:9876/api/stream/context

# Emotional state
curl http://localhost:9876/api/stream/emotions

# Behavioral patterns
curl http://localhost:9876/api/stream/behavior
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-org/hra-chrome-extension.git
cd hra-chrome-extension

# Install dependencies
cd local-ai-server
npm install

# Start development server
npm run dev

# Run tests
npm test
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

## 📞 Support

### Getting Help

- **Documentation**: [Complete guides and API reference](./docs/)
- **GitHub Issues**: [Report bugs and request features](https://github.com/your-org/hra-chrome-extension/issues)
- **Discord**: [Join our developer community](https://discord.gg/hra-dev)
- **Email**: <EMAIL>

---

**🎉 Ready to build emotion-aware AI?** Start with our [Quick Start Guide](./docs/QUICK_START.md) and join the future of contextual AI interactions!
