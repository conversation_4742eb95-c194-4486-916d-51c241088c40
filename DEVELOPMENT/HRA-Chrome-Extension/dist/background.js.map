{"version": 3, "file": "background.js", "mappings": "AA+iBA", "sources": ["webpack://hra-chrome-extension/./src/background.js"], "sourcesContent": ["// HRA Chrome Extension - Background Service Worker\n// Manages connection to Local AI Server and coordinates all modules\n\nclass HRABackground {\n    constructor() {\n        this.LAS_URL = 'ws://localhost:9876';\n        this.socket = null;\n        this.isConnected = false;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 10;\n        this.reconnectDelay = 1000;\n        \n        // Message queues\n        this.messageQueue = [];\n        this.contextBuffer = [];\n        \n        // Module states\n        this.modules = {\n            textCapture: { enabled: true },\n            morphcast: { enabled: false },\n            behavioral: { enabled: true }\n        };\n        \n        // User preferences\n        this.userPreferences = {\n            captureMode: 'selective', // 'all' or 'selective'\n            emotionCapture: true,\n            privacyMode: false\n        };\n        \n        this.initialize();\n    }\n\n    async initialize() {\n        // Load stored preferences\n        const stored = await chrome.storage.local.get(['userPreferences', 'lasConnectionStatus']);\n        if (stored.userPreferences) {\n            this.userPreferences = { ...this.userPreferences, ...stored.userPreferences };\n        }\n        \n        // Set up message listeners\n        this.setupMessageListeners();\n        \n        // Attempt to connect to LAS\n        this.connectToLAS();\n        \n        // Set up periodic context sync\n        this.setupPeriodicSync();\n        \n        console.log('HRA Background Service initialized');\n    }\n\n    connectToLAS() {\n        if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n            return;\n        }\n\n        try {\n            this.socket = new WebSocket(this.LAS_URL);\n            \n            this.socket.onopen = () => {\n                console.log('Connected to Local AI Server');\n                this.isConnected = true;\n                this.reconnectAttempts = 0;\n                \n                // Send authentication/handshake\n                this.send({\n                    type: 'handshake',\n                    source: 'chrome_extension',\n                    version: '1.0.0',\n                    modules: this.modules\n                });\n                \n                // Process queued messages\n                this.processMessageQueue();\n                \n                // Update connection status\n                chrome.storage.local.set({ lasConnectionStatus: 'connected' });\n                this.broadcastConnectionStatus('connected');\n            };\n            \n            this.socket.onmessage = (event) => {\n                this.handleLASMessage(JSON.parse(event.data));\n            };\n            \n            this.socket.onerror = (error) => {\n                console.error('LAS connection error:', error);\n                this.isConnected = false;\n                chrome.storage.local.set({ lasConnectionStatus: 'disconnected' });\n                this.broadcastConnectionStatus('disconnected');\n            };\n            \n            this.socket.onclose = () => {\n                console.log('Disconnected from Local AI Server');\n                this.isConnected = false;\n                chrome.storage.local.set({ lasConnectionStatus: 'disconnected' });\n                this.broadcastConnectionStatus('disconnected');\n                \n                // Attempt reconnection\n                if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                    setTimeout(() => {\n                        this.reconnectAttempts++;\n                        this.connectToLAS();\n                    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));\n                }\n            };\n        } catch (error) {\n            console.error('Failed to connect to LAS:', error);\n            this.isConnected = false;\n            chrome.storage.local.set({ lasConnectionStatus: 'disconnected' });\n            this.broadcastConnectionStatus('disconnected');\n\n            // Try HTTP fallback\n            this.tryHttpFallback();\n        }\n    }\n\n    async tryHttpFallback() {\n        try {\n            const response = await fetch('http://localhost:9876/api/llm/status');\n            if (response.ok) {\n                console.log('HTTP connection to LAS successful - WebSocket unavailable');\n                this.isConnected = true; // Mark as connected for HTTP mode\n                chrome.storage.local.set({ lasConnectionStatus: 'connected' });\n                this.broadcastConnectionStatus('connected');\n            }\n        } catch (error) {\n            console.log('Both WebSocket and HTTP connections failed');\n        }\n    }\n\n    setupMessageListeners() {\n        // Listen for messages from content scripts and popup\n        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {\n            switch (request.type) {\n                case 'capturedText':\n                    this.handleCapturedText(request.data, sender.tab);\n                    break;\n                    \n                case 'emotionData':\n                    this.handleEmotionData(request.data, sender.tab);\n                    break;\n                    \n                case 'behavioralData':\n                    this.handleBehavioralData(request.data, sender.tab);\n                    break;\n                    \n                case 'getLASStatus':\n                    sendResponse({ connected: this.isConnected });\n                    break;\n\n                case 'getSessionStats':\n                    this.getSessionStats(sendResponse);\n                    return true; // Will respond asynchronously\n\n                case 'updatePreferences':\n                    this.updatePreferences(request.preferences);\n                    break;\n\n                case 'toggleModule':\n                    this.toggleModule(request.module, request.enabled);\n                    break;\n\n                case 'requestContext':\n                    this.requestContextFromLAS(request.query, sendResponse);\n                    return true; // Will respond asynchronously\n\n                case 'testCommunication':\n                    this.testCommunication(sendResponse);\n                    return true; // Will respond asynchronously\n\n                default:\n                    console.warn('Unknown message type:', request.type);\n            }\n        });\n\n        // Listen for tab events for behavioral tracking\n        chrome.tabs.onActivated.addListener((activeInfo) => {\n            if (this.modules.behavioral.enabled) {\n                this.trackTabActivity('activated', activeInfo);\n            }\n        });\n\n        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {\n            if (this.modules.behavioral.enabled && changeInfo.status === 'complete') {\n                this.trackTabActivity('navigated', { tabId, url: tab.url, title: tab.title });\n            }\n        });\n\n        chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {\n            if (this.modules.behavioral.enabled) {\n                this.trackTabActivity('closed', { tabId });\n            }\n        });\n    }\n\n    handleCapturedText(data, tab) {\n        const contextEntry = {\n            type: 'text_capture',\n            timestamp: Date.now(),\n            source: {\n                tabId: tab.id,\n                url: tab.url,\n                title: tab.title\n            },\n            data: {\n                text: data.text,\n                elementType: data.elementType,\n                action: data.action\n            }\n        };\n        \n        this.addToContextBuffer(contextEntry);\n        \n        // Send to LAS if connected\n        if (this.isConnected) {\n            this.send({\n                type: 'context_update',\n                entry: contextEntry\n            });\n        } else {\n            this.queueMessage(contextEntry);\n        }\n    }\n\n    handleEmotionData(data, tab) {\n        const emotionEntry = {\n            type: 'emotion_capture',\n            timestamp: Date.now(),\n            source: {\n                tabId: tab.id,\n                url: tab.url\n            },\n            data: data\n        };\n        \n        // Always send emotion data immediately if connected\n        if (this.isConnected) {\n            this.send({\n                type: 'emotion_update',\n                entry: emotionEntry\n            });\n        }\n        \n        // Also add to context buffer for correlation\n        this.addToContextBuffer(emotionEntry);\n    }\n\n    handleBehavioralData(data, tab) {\n        const behavioralEntry = {\n            type: 'behavioral_capture',\n            timestamp: Date.now(),\n            source: {\n                tabId: tab.id,\n                url: tab.url\n            },\n            data: data\n        };\n        \n        this.addToContextBuffer(behavioralEntry);\n        \n        if (this.isConnected) {\n            this.send({\n                type: 'behavioral_update',\n                entry: behavioralEntry\n            });\n        }\n    }\n\n    trackTabActivity(action, details) {\n        const activityEntry = {\n            type: 'tab_activity',\n            timestamp: Date.now(),\n            action: action,\n            details: details\n        };\n        \n        this.addToContextBuffer(activityEntry);\n        \n        if (this.isConnected) {\n            this.send({\n                type: 'activity_update',\n                entry: activityEntry\n            });\n        }\n    }\n\n    handleLASMessage(message) {\n        switch (message.type) {\n            case 'handshake_ack':\n                console.log('LAS handshake acknowledged');\n                break;\n                \n            case 'context_response':\n                // Forward to appropriate tab or popup\n                if (message.requestId) {\n                    this.forwardContextResponse(message);\n                }\n                break;\n                \n            case 'config_update':\n                this.updateConfiguration(message.config);\n                break;\n                \n            case 'module_control':\n                this.handleModuleControl(message);\n                break;\n                \n            case 'proactive_suggestion':\n                this.handleProactiveSuggestion(message);\n                break;\n                \n            default:\n                console.log('Received message from LAS:', message);\n        }\n    }\n\n    addToContextBuffer(entry) {\n        this.contextBuffer.push(entry);\n        \n        // Keep buffer size manageable (last 100 entries)\n        if (this.contextBuffer.length > 100) {\n            this.contextBuffer = this.contextBuffer.slice(-100);\n        }\n        \n        // Store in local storage for persistence\n        chrome.storage.local.set({ \n            contextBuffer: this.contextBuffer.slice(-50) // Store last 50 for efficiency\n        });\n    }\n\n    send(message) {\n        if (this.socket && this.socket.readyState === WebSocket.OPEN) {\n            this.socket.send(JSON.stringify(message));\n        } else {\n            console.warn('Cannot send message - LAS not connected');\n            this.queueMessage(message);\n        }\n    }\n\n    queueMessage(message) {\n        this.messageQueue.push(message);\n        \n        // Limit queue size\n        if (this.messageQueue.length > 1000) {\n            this.messageQueue = this.messageQueue.slice(-500);\n        }\n    }\n\n    processMessageQueue() {\n        while (this.messageQueue.length > 0 && this.isConnected) {\n            const message = this.messageQueue.shift();\n            this.send(message);\n        }\n    }\n\n    setupPeriodicSync() {\n        // Sync context buffer every 30 seconds\n        setInterval(() => {\n            if (this.isConnected && this.contextBuffer.length > 0) {\n                this.send({\n                    type: 'bulk_context_sync',\n                    entries: this.contextBuffer.slice(-20), // Send last 20 entries\n                    timestamp: Date.now()\n                });\n            }\n        }, 30000);\n    }\n\n    updatePreferences(preferences) {\n        this.userPreferences = { ...this.userPreferences, ...preferences };\n        chrome.storage.local.set({ userPreferences: this.userPreferences });\n        \n        // Notify all tabs of preference changes\n        chrome.tabs.query({}, (tabs) => {\n            tabs.forEach(tab => {\n                chrome.tabs.sendMessage(tab.id, {\n                    type: 'preferencesUpdated',\n                    preferences: this.userPreferences\n                });\n            });\n        });\n        \n        // Notify LAS\n        if (this.isConnected) {\n            this.send({\n                type: 'preferences_update',\n                preferences: this.userPreferences\n            });\n        }\n    }\n\n    toggleModule(moduleName, enabled) {\n        if (this.modules[moduleName] !== undefined) {\n            this.modules[moduleName].enabled = enabled;\n            \n            // Special handling for morphcast\n            if (moduleName === 'morphcast') {\n                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {\n                    if (tabs[0]) {\n                        chrome.tabs.sendMessage(tabs[0].id, {\n                            type: 'toggleMorphcast',\n                            enabled: enabled\n                        });\n                    }\n                });\n            }\n            \n            // Notify LAS\n            if (this.isConnected) {\n                this.send({\n                    type: 'module_status_update',\n                    module: moduleName,\n                    enabled: enabled\n                });\n            }\n        }\n    }\n\n    requestContextFromLAS(query, sendResponse) {\n        const requestId = Date.now().toString();\n        \n        // Store callback for later\n        this.pendingRequests = this.pendingRequests || {};\n        this.pendingRequests[requestId] = sendResponse;\n        \n        this.send({\n            type: 'context_request',\n            requestId: requestId,\n            query: query\n        });\n        \n        // Timeout after 5 seconds\n        setTimeout(() => {\n            if (this.pendingRequests[requestId]) {\n                this.pendingRequests[requestId]({ error: 'Request timeout' });\n                delete this.pendingRequests[requestId];\n            }\n        }, 5000);\n    }\n\n    forwardContextResponse(message) {\n        if (this.pendingRequests && this.pendingRequests[message.requestId]) {\n            this.pendingRequests[message.requestId](message.data);\n            delete this.pendingRequests[message.requestId];\n        }\n    }\n\n    handleProactiveSuggestion(message) {\n        // Create a notification or update the extension badge\n        chrome.action.setBadgeText({ text: '!' });\n        chrome.action.setBadgeBackgroundColor({ color: '#667eea' });\n        \n        // Store the suggestion\n        chrome.storage.local.set({ \n            latestProactiveSuggestion: message.suggestion,\n            hasUnreadSuggestion: true\n        });\n    }\n\n    broadcastConnectionStatus(status) {\n        chrome.tabs.query({}, (tabs) => {\n            tabs.forEach(tab => {\n                chrome.tabs.sendMessage(tab.id, {\n                    type: 'lasConnectionStatus',\n                    status: status\n                });\n            });\n        });\n    }\n\n    updateConfiguration(config) {\n        // Update local configuration based on LAS instructions\n        if (config.modules) {\n            Object.keys(config.modules).forEach(module => {\n                if (this.modules[module]) {\n                    this.modules[module] = { ...this.modules[module], ...config.modules[module] };\n                }\n            });\n        }\n        \n        console.log('Configuration updated from LAS:', config);\n    }\n\n    handleModuleControl(message) {\n        // LAS can remotely control modules\n        if (message.action === 'enable') {\n            this.toggleModule(message.module, true);\n        } else if (message.action === 'disable') {\n            this.toggleModule(message.module, false);\n        }\n    }\n\n    async getSessionStats(sendResponse) {\n        try {\n            // Get stats from storage\n            const stored = await chrome.storage.local.get(['sessionStats']);\n            const stats = stored.sessionStats || {\n                selections: 0,\n                copies: 0,\n                focusPercentage: 100,\n                scrollDepth: 0,\n                clicks: 0\n            };\n\n            // Add real-time data\n            stats.contextBufferSize = this.contextBuffer.length;\n            stats.isConnected = this.isConnected;\n            stats.queuedMessages = this.messageQueue.length;\n\n            sendResponse(stats);\n        } catch (error) {\n            console.error('Failed to get session stats:', error);\n            sendResponse({ error: 'Failed to get session stats' });\n        }\n    }\n\n    async testCommunication(sendResponse) {\n        try {\n            // Test communication with all active tabs\n            const tabs = await chrome.tabs.query({});\n            const results = [];\n\n            for (const tab of tabs) {\n                try {\n                    const response = await chrome.tabs.sendMessage(tab.id, { type: 'ping' });\n                    results.push({\n                        tabId: tab.id,\n                        url: tab.url,\n                        title: tab.title,\n                        status: 'success',\n                        response: response\n                    });\n                } catch (error) {\n                    results.push({\n                        tabId: tab.id,\n                        url: tab.url,\n                        title: tab.title,\n                        status: 'error',\n                        error: error.message\n                    });\n                }\n            }\n\n            sendResponse({\n                status: 'Communication test completed',\n                isConnectedToLAS: this.isConnected,\n                totalTabs: tabs.length,\n                results: results,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error('Failed to test communication:', error);\n            sendResponse({ error: 'Failed to test communication', details: error.message });\n        }\n    }\n}\n\n// Initialize the background service\nconst hraBackground = new HRABackground();\n\n// Keep service worker alive\nchrome.runtime.onInstalled.addListener(() => {\n    console.log('HRA Chrome Extension installed');\n});\n"], "names": [], "sourceRoot": ""}