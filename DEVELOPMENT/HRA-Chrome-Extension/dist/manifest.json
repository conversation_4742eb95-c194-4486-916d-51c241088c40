{"manifest_version": 3, "name": "Harmonic Resonance Agent", "version": "1.0.0", "description": "Client-side AI companion with persistent emotional memory", "permissions": ["storage", "activeTab", "scripting", "tabs", "webNavigation"], "host_permissions": ["http://localhost:9876/*", "https://sdk.morphcast.com/*", "<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["inject.js", "morphcast-loader.js"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' http://localhost:9876 https://sdk.morphcast.com ws://localhost:9876"}}