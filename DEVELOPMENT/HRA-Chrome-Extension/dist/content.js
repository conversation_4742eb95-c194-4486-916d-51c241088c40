(()=>{class t{constructor(){this.isActive=!1,this.userPreferences={},this.modules={textCapture:!0,morphcast:!1,behavioral:!0},this.selectedText="",this.lastCapturedText="",this.captureMode="selective",this.scrollData={position:0,velocity:0,direction:null,lastUpdate:Date.now(),pauses:[]},this.clickData={clickCount:0,lastClick:null,clickPattern:[]},this.textInteractionData={selections:0,averageSelectionLength:0,copyEvents:0},this.pageEngagement={startTime:Date.now(),focusTime:0,lastFocusStart:Date.now(),visibilityChanges:0,scrollDepth:0,maxScrollDepth:0},this.sensitivePatterns=[/\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/,/\b\d{3}[\s-]?\d{2}[\s-]?\d{4}\b/,/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/,/\b\d{10,15}\b/],this.initialize()}async initialize(){await chrome.runtime.sendMessage({type:"getLASStatus"}),this.setupEventListeners(),this.initializeBehavioralTracking(),chrome.runtime.onMessage.addListener(((t,e,a)=>{this.handleMessage(t,e,a)})),this.isActive=!0,console.log("HRA Content Script initialized on:",window.location.href)}handleMessage(t,e,a){console.log("Message received in content script:",t);try{switch(t.type){case"preferencesUpdated":this.updatePreferences(t.preferences),a({status:"Preferences updated successfully"});break;case"toggleMorphcast":this.toggleMorphcast(t.enabled),a({status:`MorphCast ${t.enabled?"enabled":"disabled"} successfully`});break;case"lasConnectionStatus":this.handleLASStatusChange(t.status),a({status:"LAS status updated successfully"});break;case"capturePageContent":this.capturePageContent(),a({status:"Page content captured successfully"});break;case"ping":a({status:"Content script is active",url:window.location.href});break;default:console.log("Unknown message type:",t.type),a({status:"Unknown message type",type:t.type})}}catch(t){console.error("Error handling message:",t),a({status:"Error handling message",error:t.message})}return!0}setupEventListeners(){if(document.addEventListener("mouseup",(()=>{this.modules.textCapture&&this.handleTextSelection()})),document.addEventListener("keyup",(t=>{this.modules.textCapture&&(t.ctrlKey||t.metaKey)&&"c"===t.key&&this.handleCopyEvent()})),this.modules.behavioral){let t;window.addEventListener("scroll",(e=>{this.trackScrollBehavior(),clearTimeout(t),t=setTimeout((()=>{this.recordScrollPause()}),150)})),document.addEventListener("click",(t=>{this.trackClickBehavior(t)})),window.addEventListener("focus",(()=>{this.pageEngagement.lastFocusStart=Date.now()})),window.addEventListener("blur",(()=>{this.pageEngagement.lastFocusStart&&(this.pageEngagement.focusTime+=Date.now()-this.pageEngagement.lastFocusStart)})),document.addEventListener("visibilitychange",(()=>{this.pageEngagement.visibilityChanges++,document.hidden?this.pageEngagement.lastFocusStart&&(this.pageEngagement.focusTime+=Date.now()-this.pageEngagement.lastFocusStart):this.pageEngagement.lastFocusStart=Date.now(),this.sendBehavioralUpdate("visibility",{hidden:document.hidden,visibilityChanges:this.pageEngagement.visibilityChanges})})),document.addEventListener("input",(t=>{"input"!==t.target.tagName.toLowerCase()&&"textarea"!==t.target.tagName.toLowerCase()||this.trackFormInteraction(t)}))}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>{this.onPageReady()})):this.onPageReady()}initializeBehavioralTracking(){this.updateScrollDepth(),setInterval((()=>{this.reportPageEngagement()}),3e4),this.captureInitialPageMetrics()}handleTextSelection(){const t=window.getSelection(),e=t.toString().trim();if(e.length>0&&e!==this.lastCapturedText){if(this.containsSensitiveInfo(e))return void console.log("Sensitive information detected in selection - not capturing");this.selectedText=e,this.lastCapturedText=e,this.textInteractionData.selections++;const a=this.textInteractionData.averageSelectionLength;this.textInteractionData.averageSelectionLength=(a*(this.textInteractionData.selections-1)+e.length)/this.textInteractionData.selections;const n=t.anchorNode.parentElement,s=this.getElementContext(n);this.sendTextCapture({text:e,elementType:s.type,elementClass:s.className,elementId:s.id,action:"selection",context:this.getPageContext(),timestamp:Date.now()})}}handleCopyEvent(){this.selectedText.length>0&&(this.textInteractionData.copyEvents++,this.sendTextCapture({text:this.selectedText,action:"copy",context:this.getPageContext(),timestamp:Date.now()}))}trackScrollBehavior(){const t=window.pageYOffset,e=Date.now(),a=e-this.scrollData.lastUpdate;if(a>0){const n=t-this.scrollData.position,s=Math.abs(n)/a;this.scrollData.velocity=s,this.scrollData.direction=n>0?"down":"up",this.scrollData.position=t,this.scrollData.lastUpdate=e,this.updateScrollDepth()}}recordScrollPause(){const t={position:this.scrollData.position,timestamp:Date.now(),duration:null};this.scrollData.pauses.push(t),this.scrollData.pauses.length>10&&(this.scrollData.pauses=this.scrollData.pauses.slice(-10)),this.sendBehavioralUpdate("scroll_pause",{position:t.position,scrollDepth:this.pageEngagement.scrollDepth,timestamp:t.timestamp})}trackClickBehavior(t){const e=Date.now(),a={x:t.clientX,y:t.clientY,target:t.target.tagName.toLowerCase(),targetClass:t.target.className,targetId:t.target.id,timestamp:e};this.clickData.clickCount++,this.clickData.lastClick=a,this.clickData.clickPattern.push(a),this.clickData.clickPattern.length>20&&(this.clickData.clickPattern=this.clickData.clickPattern.slice(-20)),this.sendBehavioralUpdate("click",a)}trackFormInteraction(t){const e={inputType:t.target.type,inputName:t.target.name,inputId:t.target.id,valueLength:t.target.value.length,timestamp:Date.now()};this.sendBehavioralUpdate("form_interaction",e)}updateScrollDepth(){const t=window.pageYOffset,e=window.innerHeight,a=document.documentElement.scrollHeight,n=Math.round((t+e)/a*100);this.pageEngagement.scrollDepth=Math.min(n,100),this.pageEngagement.maxScrollDepth=Math.max(this.pageEngagement.maxScrollDepth,this.pageEngagement.scrollDepth)}reportPageEngagement(){const t=Date.now(),e=t-this.pageEngagement.startTime;let a=this.pageEngagement.focusTime;!document.hidden&&this.pageEngagement.lastFocusStart&&(a+=t-this.pageEngagement.lastFocusStart);const n={sessionDuration:e,focusTime:a,focusPercentage:Math.round(a/e*100),scrollDepth:this.pageEngagement.scrollDepth,maxScrollDepth:this.pageEngagement.maxScrollDepth,visibilityChanges:this.pageEngagement.visibilityChanges,clickCount:this.clickData.clickCount,textSelections:this.textInteractionData.selections,averageSelectionLength:this.textInteractionData.averageSelectionLength,copyEvents:this.textInteractionData.copyEvents,url:window.location.href,title:document.title};this.sendBehavioralUpdate("engagement_report",n)}captureInitialPageMetrics(){const t={url:window.location.href,title:document.title,referrer:document.referrer,loadTime:Date.now()-this.pageEngagement.startTime,documentHeight:document.documentElement.scrollHeight,viewportHeight:window.innerHeight,timestamp:Date.now()};this.sendBehavioralUpdate("page_load",t)}capturePageContent(){const t={title:document.title,headings:this.extractHeadings(),mainContent:this.extractMainContent(),links:this.extractLinks(),images:this.extractImages(),timestamp:Date.now()};this.sendTextCapture({text:JSON.stringify(t),action:"page_content",context:this.getPageContext(),timestamp:Date.now()})}extractHeadings(){const t=[];return document.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach((e=>{t.push({level:e.tagName.toLowerCase(),text:e.textContent.trim(),id:e.id})})),t}extractMainContent(){const t=["main","article",".content",".main-content","#content","#main",".post-content",".entry-content"];for(const e of t){const t=document.querySelector(e);if(t)return t.textContent.trim().substring(0,2e3)}return document.body.textContent.trim().substring(0,2e3)}extractLinks(){const t=[];return document.querySelectorAll("a[href]").forEach((e=>{t.push({text:e.textContent.trim(),href:e.href,title:e.title})})),t.slice(0,20)}extractImages(){const t=[];return document.querySelectorAll("img[src]").forEach((e=>{t.push({src:e.src,alt:e.alt,title:e.title})})),t.slice(0,10)}getElementContext(t){return{type:t.tagName.toLowerCase(),className:t.className,id:t.id,textContent:t.textContent.trim().substring(0,100)}}getPageContext(){return{url:window.location.href,title:document.title,domain:window.location.hostname,timestamp:Date.now()}}containsSensitiveInfo(t){return this.sensitivePatterns.some((e=>e.test(t)))}toggleMorphcast(t){this.modules.morphcast=t,t?this.initializeMorphcast():this.destroyMorphcast()}initializeMorphcast(){const t=document.createElement("script");t.src=chrome.runtime.getURL("inject.js"),t.onload=()=>{console.log("MorphCast injection script loaded")},(document.head||document.documentElement).appendChild(t)}destroyMorphcast(){window.postMessage({type:"STOP_MORPHCAST"},"*"),window.addEventListener("message",(t=>{if(t.source===window&&t.data)switch(t.data.type){case"MORPHCAST_STOPPED":console.log("MorphCast stopped successfully");break;case"MORPHCAST_UNAVAILABLE":console.warn("MorphCast unavailable:",t.data.error),this.modules.morphcast=!1;break;case"EMOTION_UPDATE":this.handleEmotionUpdate(t.data.data)}}))}handleEmotionUpdate(t){t&&this.modules.morphcast&&chrome.runtime.sendMessage({type:"emotionData",data:t})}onPageReady(){this.captureInitialPageMetrics(),"all"===this.captureMode&&setTimeout((()=>{this.capturePageContent()}),2e3)}updatePreferences(t){this.userPreferences=t,this.captureMode=t.captureMode||"selective",void 0!==t.emotionCapture&&(this.modules.morphcast=t.emotionCapture)}handleLASStatusChange(t){console.log("LAS connection status changed:",t),"connected"===t?this.showConnectionIndicator(!0):this.showConnectionIndicator(!1)}showConnectionIndicator(t){const e=document.getElementById("hra-connection-indicator");e&&e.remove();const a=document.createElement("div");a.id="hra-connection-indicator",a.style.cssText=`\n            position: fixed;\n            top: 10px;\n            right: 10px;\n            z-index: 10000;\n            padding: 5px 10px;\n            border-radius: 15px;\n            font-size: 12px;\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            color: white;\n            background-color: ${t?"#4CAF50":"#f44336"};\n            box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n            transition: opacity 0.3s ease;\n        `,a.textContent="HRA "+(t?"Connected":"Disconnected"),document.body.appendChild(a),setTimeout((()=>{a.parentNode&&(a.style.opacity="0",setTimeout((()=>{a.parentNode&&a.remove()}),300))}),3e3)}sendTextCapture(t){chrome.runtime.sendMessage({type:"capturedText",data:t})}sendBehavioralUpdate(t,e){chrome.runtime.sendMessage({type:"behavioralData",data:{eventType:t,...e}})}sendEmotionData(t){chrome.runtime.sendMessage({type:"emotionData",data:t})}}if(window.location.href.startsWith("http")){const e=new t;window.hraContent=e}})();