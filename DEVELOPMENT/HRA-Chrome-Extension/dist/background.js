new class{constructor(){this.LAS_URL="ws://localhost:9876",this.LAS_HTTP_URL="http://localhost:9876",this.subscriberId=null,this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectDelay=1e3,this.messageQueue=[],this.contextBuffer=[],this.modules={textCapture:{enabled:!0},morphcast:{enabled:!1},behavioral:{enabled:!0}},this.userPreferences={captureMode:"selective",emotionCapture:!0,privacyMode:!1},this.initialize()}async initialize(){const e=await chrome.storage.local.get(["userPreferences","lasConnectionStatus"]);e.userPreferences&&(this.userPreferences={...this.userPreferences,...e.userPreferences}),this.setupMessageListeners(),this.connectToLAS(),this.setupPeriodicSync(),console.log("HRA Background Service initialized")}connectToLAS(){if(!this.socket||this.socket.readyState!==WebSocket.OPEN)try{this.socket=new WebSocket(this.LAS_URL),this.socket.onopen=()=>{console.log("Connected to Local AI Server"),this.isConnected=!0,this.reconnectAttempts=0,this.send({type:"handshake",source:"chrome_extension",version:"1.0.0",modules:this.modules}),this.processMessageQueue(),chrome.storage.local.set({lasConnectionStatus:"connected"}),this.broadcastConnectionStatus("connected")},this.socket.onmessage=e=>{this.handleLASMessage(JSON.parse(e.data))},this.socket.onerror=e=>{console.error("LAS connection error:",e),this.isConnected=!1,chrome.storage.local.set({lasConnectionStatus:"disconnected"}),this.broadcastConnectionStatus("disconnected")},this.socket.onclose=()=>{console.log("Disconnected from Local AI Server"),this.isConnected=!1,chrome.storage.local.set({lasConnectionStatus:"disconnected"}),this.broadcastConnectionStatus("disconnected"),this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout((()=>{this.reconnectAttempts++,this.connectToLAS()}),this.reconnectDelay*Math.pow(2,this.reconnectAttempts))}}catch(e){console.error("Failed to connect to LAS:",e),this.isConnected=!1,chrome.storage.local.set({lasConnectionStatus:"disconnected"}),this.broadcastConnectionStatus("disconnected"),this.tryHttpFallback()}}async tryHttpFallback(){try{(await fetch(`${this.LAS_HTTP_URL}/api/llm/status`)).ok&&(console.log("HTTP connection to LAS successful - WebSocket unavailable"),this.isConnected=!0,chrome.storage.local.set({lasConnectionStatus:"connected"}),this.broadcastConnectionStatus("connected"),await this.subscribeToEmotionStream())}catch(e){console.log("Both WebSocket and HTTP connections failed")}}async subscribeToEmotionStream(){try{const e=await fetch(`${this.LAS_HTTP_URL}/api/llm/subscribe`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({callbackUrl:`${this.LAS_HTTP_URL}/chrome-extension-callback`,events:["emotion","behavior","summary"]})});if(e.ok){const t=await e.json();this.subscriberId=t.subscriberId,console.log("Successfully subscribed to emotion stream:",t),this.broadcastMessage({type:"emotionStreamReady",subscriberId:this.subscriberId})}else console.warn("Failed to subscribe to emotion stream")}catch(e){console.error("Error subscribing to emotion stream:",e)}}setupMessageListeners(){chrome.runtime.onMessage.addListener(((e,t,s)=>{switch(e.type){case"capturedText":this.handleCapturedText(e.data,t.tab);break;case"emotionData":this.handleEmotionData(e.data,t.tab);break;case"behavioralData":this.handleBehavioralData(e.data,t.tab);break;case"getLASStatus":s({connected:this.isConnected});break;case"getSessionStats":return this.getSessionStats(s),!0;case"updatePreferences":this.updatePreferences(e.preferences);break;case"toggleModule":this.toggleModule(e.module,e.enabled);break;case"requestContext":return this.requestContextFromLAS(e.query,s),!0;case"testCommunication":return this.testCommunication(s),!0;default:console.warn("Unknown message type:",e.type)}})),chrome.tabs.onActivated.addListener((e=>{this.modules.behavioral.enabled&&this.trackTabActivity("activated",e)})),chrome.tabs.onUpdated.addListener(((e,t,s)=>{this.modules.behavioral.enabled&&"complete"===t.status&&this.trackTabActivity("navigated",{tabId:e,url:s.url,title:s.title})})),chrome.tabs.onRemoved.addListener(((e,t)=>{this.modules.behavioral.enabled&&this.trackTabActivity("closed",{tabId:e})}))}handleCapturedText(e,t){const s={type:"text_capture",timestamp:Date.now(),source:{tabId:t.id,url:t.url,title:t.title},data:{text:e.text,elementType:e.elementType,action:e.action}};this.addToContextBuffer(s),this.isConnected?this.send({type:"context_update",entry:s}):this.queueMessage(s)}handleEmotionData(e,t){const s={type:"emotion_capture",timestamp:Date.now(),source:{tabId:t.id,url:t.url},data:e};this.isConnected&&this.send({type:"emotion_update",entry:s}),this.addToContextBuffer(s)}handleBehavioralData(e,t){const s={type:"behavioral_capture",timestamp:Date.now(),source:{tabId:t.id,url:t.url},data:e};this.addToContextBuffer(s),this.isConnected&&this.send({type:"behavioral_update",entry:s})}trackTabActivity(e,t){const s={type:"tab_activity",timestamp:Date.now(),action:e,details:t};this.addToContextBuffer(s),this.isConnected&&this.send({type:"activity_update",entry:s})}handleLASMessage(e){switch(e.type){case"handshake_ack":console.log("LAS handshake acknowledged");break;case"context_response":e.requestId&&this.forwardContextResponse(e);break;case"config_update":this.updateConfiguration(e.config);break;case"module_control":this.handleModuleControl(e);break;case"proactive_suggestion":this.handleProactiveSuggestion(e);break;default:console.log("Received message from LAS:",e)}}addToContextBuffer(e){this.contextBuffer.push(e),this.contextBuffer.length>100&&(this.contextBuffer=this.contextBuffer.slice(-100)),chrome.storage.local.set({contextBuffer:this.contextBuffer.slice(-50)})}send(e){this.socket&&this.socket.readyState===WebSocket.OPEN?this.socket.send(JSON.stringify(e)):(console.warn("Cannot send message - LAS not connected"),this.queueMessage(e))}queueMessage(e){this.messageQueue.push(e),this.messageQueue.length>1e3&&(this.messageQueue=this.messageQueue.slice(-500))}processMessageQueue(){for(;this.messageQueue.length>0&&this.isConnected;){const e=this.messageQueue.shift();this.send(e)}}setupPeriodicSync(){setInterval((()=>{this.isConnected&&this.contextBuffer.length>0&&this.send({type:"bulk_context_sync",entries:this.contextBuffer.slice(-20),timestamp:Date.now()})}),3e4)}updatePreferences(e){this.userPreferences={...this.userPreferences,...e},chrome.storage.local.set({userPreferences:this.userPreferences}),chrome.tabs.query({},(e=>{e.forEach((e=>{chrome.tabs.sendMessage(e.id,{type:"preferencesUpdated",preferences:this.userPreferences})}))})),this.isConnected&&this.send({type:"preferences_update",preferences:this.userPreferences})}toggleModule(e,t){void 0!==this.modules[e]&&(this.modules[e].enabled=t,"morphcast"===e&&chrome.tabs.query({active:!0,currentWindow:!0},(e=>{e[0]&&chrome.tabs.sendMessage(e[0].id,{type:"toggleMorphcast",enabled:t})})),this.isConnected&&this.send({type:"module_status_update",module:e,enabled:t}))}requestContextFromLAS(e,t){const s=Date.now().toString();this.pendingRequests=this.pendingRequests||{},this.pendingRequests[s]=t,this.send({type:"context_request",requestId:s,query:e}),setTimeout((()=>{this.pendingRequests[s]&&(this.pendingRequests[s]({error:"Request timeout"}),delete this.pendingRequests[s])}),5e3)}forwardContextResponse(e){this.pendingRequests&&this.pendingRequests[e.requestId]&&(this.pendingRequests[e.requestId](e.data),delete this.pendingRequests[e.requestId])}handleProactiveSuggestion(e){chrome.action.setBadgeText({text:"!"}),chrome.action.setBadgeBackgroundColor({color:"#667eea"}),chrome.storage.local.set({latestProactiveSuggestion:e.suggestion,hasUnreadSuggestion:!0})}broadcastConnectionStatus(e){chrome.tabs.query({},(t=>{t.forEach((t=>{chrome.tabs.sendMessage(t.id,{type:"lasConnectionStatus",status:e})}))}))}updateConfiguration(e){e.modules&&Object.keys(e.modules).forEach((t=>{this.modules[t]&&(this.modules[t]={...this.modules[t],...e.modules[t]})})),console.log("Configuration updated from LAS:",e)}handleModuleControl(e){"enable"===e.action?this.toggleModule(e.module,!0):"disable"===e.action&&this.toggleModule(e.module,!1)}async getSessionStats(e){try{const t=(await chrome.storage.local.get(["sessionStats"])).sessionStats||{selections:0,copies:0,focusPercentage:100,scrollDepth:0,clicks:0};t.contextBufferSize=this.contextBuffer.length,t.isConnected=this.isConnected,t.queuedMessages=this.messageQueue.length,e(t)}catch(t){console.error("Failed to get session stats:",t),e({error:"Failed to get session stats"})}}async testCommunication(e){try{const t=await chrome.tabs.query({}),s=[];for(const e of t)try{const t=await chrome.tabs.sendMessage(e.id,{type:"ping"});s.push({tabId:e.id,url:e.url,title:e.title,status:"success",response:t})}catch(t){s.push({tabId:e.id,url:e.url,title:e.title,status:"error",error:t.message})}e({status:"Communication test completed",isConnectedToLAS:this.isConnected,totalTabs:t.length,results:s,timestamp:(new Date).toISOString()})}catch(t){console.error("Failed to test communication:",t),e({error:"Failed to test communication",details:t.message})}}},chrome.runtime.onInstalled.addListener((e=>{console.log("HRA Chrome Extension installed or updated:",e.reason),"update"!==e.reason&&"install"!==e.reason||chrome.tabs.query({},(e=>{for(const t of e)if(t.url&&t.url.startsWith("http"))try{chrome.tabs.reload(t.id)}catch(e){console.warn(`Could not reload tab ${t.id}:`,e.message)}}))}));
//# sourceMappingURL=background.js.map