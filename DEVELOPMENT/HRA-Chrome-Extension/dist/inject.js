(()=>{class t{constructor(){this.isInitialized=!1,this.isRunning=!1,this.cyInstance=null,this.startFunction=null,this.stopFunction=null,this.config={licenseKey:"sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b",email:"gabemc<PERSON><EMAIL>"},this.emotionHistory=[],this.currentEmotionState=null,this.sessionStartTime=Date.now(),this.lastEmotionUpdate=0,this.autoStopTimeout=9e5,this.autoStopTimer=null,this.initialize()}async initialize(){try{window.addEventListener("message",(t=>{t.source===window&&t.data&&this.handleMessage(t.data)})),await this.loadMorphCastSDK(),console.log("HRA MorphCast Module initialized successfully"),this.isInitialized=!0}catch(t){console.error("Failed to initialize HRA MorphCast Module:",t),this.isInitialized=!1,this.sendMessage({type:"MORPHCAST_UNAVAILABLE",error:t.message})}}async loadMorphCastSDK(){return new Promise(((t,e)=>{if(window.CY&&window.MphTools)return void t();let s=0;const o=[],i=()=>{3===s?window.CY&&window.MphTools?(console.log("Modern MorphCast SDK loaded successfully"),t()):e(new Error("Scripts loaded but CY/MphTools not available")):o.length>0&&e(new Error(`Failed to load scripts: ${o.join(", ")}`))},a=(t,e)=>{const a=document.createElement("script");a.src=t,a.async=!0,a.crossOrigin="anonymous",a.onload=()=>{console.log(`${e} loaded successfully`),s++,i()},a.onerror=()=>{console.error(`Failed to load ${e}`),o.push(e),i()},document.head.appendChild(a)};a("https://sdk.morphcast.com/mphtools/v1.1/mphtools.js","MphTools"),a("https://ai-sdk.morphcast.com/v1.16/ai-sdk.js","AI-SDK"),a("https://sdk.morphcast.com/emotion-statistics/v1.0-beta/script.js","Statistics")}))}handleMessage(t){switch(t.type){case"START_MORPHCAST":this.startEmotionCapture();break;case"STOP_MORPHCAST":this.stopEmotionCapture();break;case"GET_EMOTION_STATE":this.sendCurrentEmotionState();break;default:console.log("Unknown message type:",t.type)}}async startEmotionCapture(){if(!this.isRunning&&this.isInitialized)try{console.log("Starting HRA emotion capture with modern API..."),window.MphTools&&window.MphTools.CameraPrivacyPopup&&window.MphTools.CameraPrivacyPopup.setText({title:"HRA Camera Access",description:"Harmonic Resonance Agent needs camera access for emotion detection. Your privacy is protected - data stays local.",url:"about:blank"});const t={sendDataInterval:5e3,tickInterval:1e3,stopAfter:this.autoStopTimeout,licenseKey:this.config.licenseKey};this.statisticsUploader=new window.MorphCastStatistics.StatisticsUploader(t);const e=window.CY.loader().licenseKey(this.config.licenseKey).addModule(window.CY.modules().FACE_AROUSAL_VALENCE.name,{smoothness:.7}).addModule(window.CY.modules().FACE_EMOTION.name,{smoothness:.4}).addModule(window.CY.modules().FACE_ATTENTION.name,{smoothness:.83}).addModule(window.CY.modules().ALARM_LOW_ATTENTION.name,{timeWindowMs:5e3,initialToleranceMs:7e3,threshold:.33}).addModule(window.CY.modules().FACE_POSE.name,{smoothness:.65}).addModule(window.CY.modules().FACE_AGE.name,{rawOutput:!1}).addModule(window.CY.modules().FACE_GENDER.name,{smoothness:.95,threshold:.7}).addModule(window.CY.modules().FACE_FEATURES.name,{smoothness:.9}).addModule(window.CY.modules().FACE_DETECTOR.name,{maxInputFrameSize:320,smoothness:.83}).addModule(window.CY.modules().DATA_AGGREGATOR.name,{initialWaitMs:2e3,periodMs:1e3}).addModule(window.CY.modules().FACE_POSITIVITY.name,{smoothness:.4,gain:2,angle:17}),{start:s,stop:o}=await e.load();this.startFunction=s,this.stopFunction=o,this.setupEventListeners(),await this.startFunction(),await this.statisticsUploader.start(),this.isRunning=!0,this.sessionStartTime=Date.now(),this.autoStopTimer=setTimeout((async()=>{console.log("Auto-stopping MorphCast after 15 minutes"),await this.stopEmotionCapture()}),this.autoStopTimeout),console.log("HRA emotion capture started successfully"),this.sendMessage({type:"MORPHCAST_STARTED"})}catch(t){console.error("Failed to start emotion capture:",t),this.cleanup(),this.sendMessage({type:"MORPHCAST_ERROR",error:t.message})}else console.warn("MorphCast already running or not initialized")}setupEventListeners(){["FACE_AROUSAL_VALENCE","FACE_EMOTION","FACE_ATTENTION","ALARM_LOW_ATTENTION","FACE_POSE","FACE_AGE","FACE_GENDER","FACE_FEATURES","FACE_DETECTOR","DATA_AGGREGATOR","FACE_POSITIVITY"].forEach((t=>{window.CY.modules()[t]&&window.addEventListener(window.CY.modules()[t].eventName,(e=>{this.handleModuleEvent(t,e.detail)}))}))}handleModuleEvent(t,e){const s=Date.now(),o={timestamp:s,sessionTime:s-this.sessionStartTime,module:t,data:e};switch(t){case"FACE_EMOTION":this.processEmotionData(o);break;case"FACE_AROUSAL_VALENCE":this.processArousalValenceData(o);break;case"FACE_ATTENTION":this.processAttentionData(o);break;case"DATA_AGGREGATOR":this.processAggregatedData(o);break;default:this.storeEmotionData(o)}s-this.lastEmotionUpdate>200&&(this.sendEmotionUpdate(o),this.lastEmotionUpdate=s)}processEmotionData(t){if(!t.data)return;const e=t.data;let s="neutral",o=0;Object.keys(e).forEach((t=>{e[t]>o&&(o=e[t],s=t)})),t.dominantEmotion=s,t.emotionStrength=o,this.storeEmotionData(t)}processArousalValenceData(t){t.data&&(t.arousal=t.data.arousal,t.valence=t.data.valence,t.quadrant=this.determineQuadrant(t.arousal,t.valence),this.storeEmotionData(t))}processAttentionData(t){t.data&&(t.attention=t.data.attention,t.engagementLevel=this.classifyEngagementLevel(t.attention),this.storeEmotionData(t))}processAggregatedData(t){this.currentEmotionState=t,this.storeEmotionData(t)}storeEmotionData(t){this.emotionHistory.push(t),this.emotionHistory.length>100&&(this.emotionHistory=this.emotionHistory.slice(-100))}determineQuadrant(t,e){return t>.2&&e>.2?"high_positive":t>.2&&e<-.2?"high_negative":t<-.2&&e>.2?"low_positive":t<-.2&&e<-.2?"low_negative":"neutral"}classifyEngagementLevel(t){return t>.8?"highly_engaged":t>.6?"engaged":t>.4?"moderately_engaged":t>.2?"low_engagement":"disengaged"}sendEmotionUpdate(t){this.sendMessage({type:"EMOTION_UPDATE",data:t})}sendCurrentEmotionState(){this.currentEmotionState&&this.sendMessage({type:"CURRENT_EMOTION_STATE",data:this.currentEmotionState})}sendMessage(t){window.postMessage(t,"*")}async stopEmotionCapture(){console.log("Stopping HRA emotion capture"),this.isRunning=!1;try{this.statisticsUploader&&await this.statisticsUploader.stop(),this.stopFunction&&await this.stopFunction(),this.autoStopTimer&&(clearTimeout(this.autoStopTimer),this.autoStopTimer=null),this.sendEmotionSummary(),this.sendMessage({type:"MORPHCAST_STOPPED"})}catch(t){console.error("Error stopping MorphCast:",t)}}sendEmotionSummary(){if(0===this.emotionHistory.length)return;const t={sessionDuration:Date.now()-this.sessionStartTime,totalEvents:this.emotionHistory.length,emotionHistory:this.emotionHistory.slice(-20),sessionStartTime:this.sessionStartTime};this.sendMessage({type:"EMOTION_SESSION_SUMMARY",data:t})}cleanup(){this.isRunning=!1,this.startFunction=null,this.stopFunction=null,this.cyInstance=null,this.autoStopTimer&&(clearTimeout(this.autoStopTimer),this.autoStopTimer=null)}}let e=null;window.addEventListener("message",(s=>{s.source===window&&s.data&&"START_MORPHCAST"===s.data.type&&(e||(e=new t),setTimeout((()=>{e.isInitialized&&e.startEmotionCapture()}),1e3))})),document.addEventListener("DOMContentLoaded",(()=>{console.log("HRA Modern MorphCast injection script loaded")}))})();