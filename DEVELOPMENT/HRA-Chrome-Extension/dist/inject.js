(()=>{class t{constructor(){this.isInitialized=!1,this.isRunning=!1,this.morphcastSDK=null,this.stream=null,this.canvas=null,this.context=null,this.video=null,this.config={licenseKey:"sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b",email:"gabemc<PERSON><EMAIL>"},this.emotionHistory=[],this.currentEmotionState=null,this.sessionStartTime=Date.now(),this.lastEmotionUpdate=0,this.emotionCircumplex={excited:{arousal:.8,valence:.8,quadrant:"high_positive"},elated:{arousal:.9,valence:.9,quadrant:"high_positive"},ecstatic:{arousal:1,valence:.9,quadrant:"high_positive"},angry:{arousal:.8,valence:-.7,quadrant:"high_negative"},frustrated:{arousal:.7,valence:-.5,quadrant:"high_negative"},annoyed:{arousal:.6,valence:-.4,quadrant:"high_negative"},calm:{arousal:-.6,valence:.5,quadrant:"low_positive"},peaceful:{arousal:-.8,valence:.7,quadrant:"low_positive"},content:{arousal:-.4,valence:.6,quadrant:"low_positive"},sad:{arousal:-.6,valence:-.6,quadrant:"low_negative"},depressed:{arousal:-.8,valence:-.8,quadrant:"low_negative"},melancholy:{arousal:-.5,valence:-.4,quadrant:"low_negative"},neutral:{arousal:0,valence:0,quadrant:"neutral"},focused:{arousal:.2,valence:.1,quadrant:"neutral"}},this.autoStopTimeout=9e5,this.autoStopTimer=null,this.initialize()}async initialize(){try{window.addEventListener("message",(t=>{t.source===window&&t.data&&this.handleMessage(t.data)}));try{await this.loadMorphCastSDK(),console.log("HRA MorphCast Module initialized successfully"),this.isInitialized=!0}catch(t){console.warn("MorphCast SDK not available, emotion detection disabled:",t.message),this.isInitialized=!1,this.sendMessage({type:"MORPHCAST_UNAVAILABLE",error:t.message})}}catch(t){console.error("Failed to initialize HRA MorphCast Module:",t),this.isInitialized=!1}}async loadMorphCastSDK(){return new Promise(((t,e)=>{if(window.MorphCast)return void t();const a=document.createElement("script");a.src="https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js",a.async=!0,a.crossOrigin="anonymous",a.onload=()=>{console.log("MorphCast SDK loaded successfully"),setTimeout((()=>{window.MorphCast?t():e(new Error("MorphCast SDK loaded but not available"))}),100)},a.onerror=()=>{console.error("Failed to load MorphCast SDK"),e(new Error("Failed to load MorphCast SDK"))},(document.head||document.documentElement).appendChild(a)}))}handleMessage(t){switch(t.type){case"START_MORPHCAST":this.startEmotionCapture();break;case"STOP_MORPHCAST":this.stopEmotionCapture();break;case"GET_EMOTION_STATE":this.sendCurrentEmotionState();break;default:console.log("Unknown message type:",t.type)}}async startEmotionCapture(){if(!this.isRunning&&this.isInitialized)try{this.stream=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:640},height:{ideal:480},facingMode:"user"}}),this.video=document.createElement("video"),this.video.style.display="none",this.video.autoplay=!0,this.video.muted=!0,this.video.srcObject=this.stream,document.body.appendChild(this.video),this.canvas=document.createElement("canvas"),this.canvas.width=640,this.canvas.height=480,this.canvas.style.display="none",this.context=this.canvas.getContext("2d"),document.body.appendChild(this.canvas),await new Promise((t=>{this.video.onloadedmetadata=t})),await this.initializeMorphCast(),this.isRunning=!0,this.sessionStartTime=Date.now(),this.autoStopTimer=setTimeout((()=>{console.log("Auto-stopping MorphCast after 15 minutes"),this.stopEmotionCapture()}),this.autoStopTimeout),this.startEmotionDetectionLoop(),console.log("HRA emotion capture started successfully"),this.sendMessage({type:"MORPHCAST_STARTED"})}catch(t){console.error("Failed to start emotion capture:",t),this.cleanup()}else console.warn("MorphCast already running or not initialized")}async initializeMorphCast(){if(!window.MorphCast)throw new Error("MorphCast SDK not loaded");this.morphcastSDK=await window.MorphCast.init({license:this.config.licenseKey,email:this.config.email,modules:[window.MorphCast.Modules.FACE_EMOTION,window.MorphCast.Modules.FACE_AROUSAL_VALENCE,window.MorphCast.Modules.FACE_ATTENTION,window.MorphCast.Modules.FACE_POSE,window.MorphCast.Modules.FACE_AGE,window.MorphCast.Modules.FACE_GENDER,window.MorphCast.Modules.FACE_FEATURES],alarms:[window.MorphCast.Alarms.LOW_ATTENTION,window.MorphCast.Alarms.NO_FACE,window.MorphCast.Alarms.MULTIPLE_FACES]}),this.morphcastSDK.addModule(window.MorphCast.Modules.DATA_AGGREGATOR,{enabled:!0,period:5e3}),console.log("MorphCast SDK initialized with modules")}startEmotionDetectionLoop(){const t=()=>{if(this.isRunning&&this.video&&this.morphcastSDK){try{this.context.drawImage(this.video,0,0,this.canvas.width,this.canvas.height);const t=this.context.getImageData(0,0,this.canvas.width,this.canvas.height);this.morphcastSDK.analyze(t.data,this.canvas.width,this.canvas.height).then((t=>{this.processEmotionResults(t)})).catch((t=>{console.error("MorphCast analysis error:",t)}))}catch(t){console.error("Frame processing error:",t)}this.isRunning&&requestAnimationFrame(t)}};t()}processEmotionResults(t){const e=Date.now();if(!t||!t.emotion)return;const a={timestamp:e,sessionTime:e-this.sessionStartTime,emotions:t.emotion||{},arousal:t.arousal_valence?.arousal||0,valence:t.arousal_valence?.valence||0,attention:t.attention?.attention||0,facePresent:t.face_present||!1,faceCount:t.face_count||0,age:t.age?.age||null,gender:t.gender?.gender||null,pose:t.pose||null},i=this.getDominantEmotion(a.emotions),s=this.mapToCircumplex(i,a.arousal,a.valence);a.dominant_emotion=i,a.circumplex=s,a.emotional_state=this.classifyEmotionalState(a),this.emotionHistory.push(a),this.currentEmotionState=a,this.emotionHistory.length>100&&(this.emotionHistory=this.emotionHistory.slice(-100)),e-this.lastEmotionUpdate>200&&(this.sendEmotionUpdate(a),this.lastEmotionUpdate=e),this.processAlarms(t.alarms)}getDominantEmotion(t){if(!t||"object"!=typeof t)return"neutral";let e="neutral",a=0;return Object.keys(t).forEach((i=>{t[i]>a&&(a=t[i],e=i)})),a>.3?e:"neutral"}mapToCircumplex(t,e,a){return this.emotionCircumplex[t]?this.emotionCircumplex[t]:{arousal:e,valence:a,quadrant:this.determineQuadrant(e,a)}}determineQuadrant(t,e){return t>.2&&e>.2?"high_positive":t>.2&&e<-.2?"high_negative":t<-.2&&e>.2?"low_positive":t<-.2&&e<-.2?"low_negative":"neutral"}classifyEmotionalState(t){const{arousal:e,valence:a,attention:i,dominant_emotion:s}=t;return{energy_level:this.classifyEnergyLevel(e),mood_valence:this.classifyMoodValence(a),engagement_level:this.classifyEngagementLevel(i),primary_emotion:s,stability:this.calculateEmotionalStability(),intensity:Math.sqrt(e*e+a*a)}}classifyEnergyLevel(t){return t>.6?"high":t>.2?"medium":t>-.2?"balanced":t>-.6?"low":"very_low"}classifyMoodValence(t){return t>.6?"very_positive":t>.2?"positive":t>-.2?"neutral":t>-.6?"negative":"very_negative"}classifyEngagementLevel(t){return t>.8?"highly_engaged":t>.6?"engaged":t>.4?"moderately_engaged":t>.2?"low_engagement":"disengaged"}calculateEmotionalStability(){if(this.emotionHistory.length<5)return"insufficient_data";const t=this.emotionHistory.slice(-5),e=this.calculateVariance(t.map((t=>t.arousal)))+this.calculateVariance(t.map((t=>t.valence)));return e<.1?"very_stable":e<.3?"stable":e<.6?"moderately_stable":e<1?"unstable":"very_unstable"}calculateVariance(t){const e=t.reduce(((t,e)=>t+e),0)/t.length;return t.map((t=>Math.pow(t-e,2))).reduce(((t,e)=>t+e),0)/t.length}processAlarms(t){t&&Object.keys(t).forEach((e=>{t[e]&&this.handleAlarm(e)}))}handleAlarm(t){const e={type:t,timestamp:Date.now(),sessionTime:Date.now()-this.sessionStartTime};console.log("MorphCast alarm triggered:",t),this.sendMessage({type:"MORPHCAST_ALARM",alarm:e})}sendEmotionUpdate(t){this.sendMessage({type:"EMOTION_UPDATE",data:t})}sendCurrentEmotionState(){this.currentEmotionState&&this.sendMessage({type:"CURRENT_EMOTION_STATE",data:this.currentEmotionState})}sendMessage(t){window.postMessage(t,"*")}stopEmotionCapture(){console.log("Stopping HRA emotion capture"),this.isRunning=!1,this.autoStopTimer&&(clearTimeout(this.autoStopTimer),this.autoStopTimer=null),this.cleanup(),this.sendEmotionSummary(),this.sendMessage({type:"MORPHCAST_STOPPED"})}cleanup(){this.stream&&(this.stream.getTracks().forEach((t=>t.stop())),this.stream=null),this.video&&this.video.parentNode&&(this.video.parentNode.removeChild(this.video),this.video=null),this.canvas&&this.canvas.parentNode&&(this.canvas.parentNode.removeChild(this.canvas),this.canvas=null),this.morphcastSDK&&(this.morphcastSDK.stop(),this.morphcastSDK=null),this.context=null}sendEmotionSummary(){if(0===this.emotionHistory.length)return;const t=Date.now()-this.sessionStartTime,e=this.generateEmotionSummary(t);this.sendMessage({type:"EMOTION_SESSION_SUMMARY",data:e})}generateEmotionSummary(t){const e=this.emotionHistory,a=e.length,i=e.reduce(((t,e)=>t+e.arousal),0)/a,s=e.reduce(((t,e)=>t+e.valence),0)/a,o=e.reduce(((t,e)=>t+e.attention),0)/a,n={};e.forEach((t=>{const e=t.dominant_emotion;n[e]=(n[e]||0)+1}));const r=Object.keys(n).sort(((t,e)=>n[e]-n[t])).slice(0,3),l=this.calculateEmotionalJourney();return{sessionDuration:t,totalFrames:a,averages:{arousal:i,valence:s,attention:o},dominantEmotions:r,emotionDistribution:n,emotionalJourney:l,stability:this.calculateOverallStability(),engagement:this.calculateOverallEngagement()}}calculateEmotionalJourney(){const t=Math.floor(this.emotionHistory.length/5),e=[];for(let a=0;a<5;a++){const i=a*t,s=Math.min(i+t,this.emotionHistory.length),o=this.emotionHistory.slice(i,s);if(o.length>0){const t=o.reduce(((t,e)=>t+e.arousal),0)/o.length,i=o.reduce(((t,e)=>t+e.valence),0)/o.length;e.push({segment:a+1,arousal:t,valence:i,quadrant:this.determineQuadrant(t,i)})}}return e}calculateOverallStability(){const t=this.emotionHistory.map((t=>t.arousal)),e=this.emotionHistory.map((t=>t.valence)),a=this.calculateVariance(t),i=this.calculateVariance(e);return{arousal_stability:a,valence_stability:i,overall_stability:(a+i)/2}}calculateOverallEngagement(){const t=this.emotionHistory.map((t=>t.attention)),e=t.reduce(((t,e)=>t+e),0)/t.length,a=Math.max(...t),i=Math.min(...t);return{average:e,maximum:a,minimum:i,range:a-i}}}let e=null;window.addEventListener("message",(a=>{a.source===window&&a.data&&"START_MORPHCAST"===a.data.type&&(e||(e=new t),setTimeout((()=>{e.isInitialized&&e.startEmotionCapture()}),1e3))})),document.addEventListener("DOMContentLoaded",(()=>{console.log("HRA MorphCast injection script loaded")}))})();