(()=>{class t{constructor(){this.isConnected=!1,this.sessionStats={selections:0,copies:0,focusPercentage:100,scrollDepth:0,clicks:0},this.modules={emotion:!1,text:!0,behavioral:!0},this.initialize()}async initialize(){try{await this.loadState(),this.setupEventListeners(),this.updateUI(),this.checkConnectionStatus(),this.startRealTimeUpdates(),console.log("HRA Popup initialized")}catch(t){console.error("Failed to initialize popup:",t)}}async loadState(){return new Promise((t=>{chrome.storage.local.get(["modules","sessionStats","userPreferences","lasConnectionStatus"],(e=>{e.modules&&(this.modules={...this.modules,...e.modules}),e.sessionStats&&(this.sessionStats={...this.sessionStats,...e.sessionStats}),e.lasConnectionStatus&&(this.isConnected="connected"===e.lasConnectionStatus),t()}))}))}setupEventListeners(){const t=document.getElementById("emotionToggle"),e=document.getElementById("textToggle"),s=document.getElementById("behavioralToggle");t&&t.addEventListener("change",(t=>{this.toggleModule("emotion",t.target.checked)})),e&&e.addEventListener("change",(t=>{this.toggleModule("text",t.target.checked)})),s&&s.addEventListener("change",(t=>{this.toggleModule("behavioral",t.target.checked)}));const n=document.getElementById("themeToggle");n&&n.addEventListener("click",(()=>{this.toggleTheme()}));const o=document.getElementById("settingsBtn");o&&o.addEventListener("click",(()=>{this.openSettings()}));const i=document.getElementById("openDashboard");i&&i.addEventListener("click",(t=>{t.preventDefault(),this.openDashboard()})),chrome.runtime.onMessage.addListener(((t,e,s)=>{this.handleMessage(t,e,s)}))}handleMessage(t,e,s){switch(t.type){case"connectionStatusUpdate":this.updateConnectionStatus(t.status);break;case"sessionStatsUpdate":this.updateSessionStats(t.stats);break;case"emotionDataUpdate":this.updateEmotionDisplay(t.data);break;default:console.log("Unknown message:",t.type)}}async toggleModule(t,e){this.modules[t]=e,chrome.storage.local.set({modules:this.modules}),chrome.runtime.sendMessage({type:"toggleModule",module:"emotion"===t?"morphcast":t,enabled:e}),this.updateModuleStatus(t,e),console.log(`Module ${t} ${e?"enabled":"disabled"}`)}updateModuleStatus(t,e){const s={text:document.getElementById("textStatus"),behavioral:document.getElementById("behavioralStatus")};if(s[t]&&(s[t].textContent=e?"text"===t?"Tracking text selections":"Monitoring engagement":"Disabled"),"emotion"===t){const t=document.getElementById("emotionContent");t&&(t.innerHTML=e?'\n                        <div class="emotion-active">\n                            <div class="emotion-indicator pulse"></div>\n                            <div class="emotion-status">Analyzing emotions...</div>\n                        </div>\n                    ':'\n                        <svg class="icon" width="40" height="40" viewBox="0 0 24 24">\n                            <circle cx="12" cy="8" r="7"></circle>\n                            <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>\n                        </svg>\n                        <div class="emotion-placeholder-text">Enable emotion detection to start capturing your emotional resonance</div>\n                    ')}}updateUI(){const t=document.getElementById("emotionToggle"),e=document.getElementById("textToggle"),s=document.getElementById("behavioralToggle");t&&(t.checked=this.modules.emotion),e&&(e.checked=this.modules.text),s&&(s.checked=this.modules.behavioral),Object.keys(this.modules).forEach((t=>{this.updateModuleStatus(t,this.modules[t])})),this.updateSessionStatsDisplay(),this.updateConnectionIndicator()}updateSessionStatsDisplay(){const t={selectionCount:document.getElementById("selectionCount"),copyCount:document.getElementById("copyCount"),focusPercentage:document.getElementById("focusPercentage"),scrollDepth:document.getElementById("scrollDepth"),clickCount:document.getElementById("clickCount")};t.selectionCount&&(t.selectionCount.textContent=this.sessionStats.selections),t.copyCount&&(t.copyCount.textContent=this.sessionStats.copies),t.focusPercentage&&(t.focusPercentage.textContent=`${this.sessionStats.focusPercentage}%`),t.scrollDepth&&(t.scrollDepth.textContent=`${this.sessionStats.scrollDepth}%`),t.clickCount&&(t.clickCount.textContent=this.sessionStats.clicks)}updateSessionStats(t){this.sessionStats={...this.sessionStats,...t},this.updateSessionStatsDisplay(),chrome.storage.local.set({sessionStats:this.sessionStats})}updateConnectionStatus(t){this.isConnected="connected"===t,this.updateConnectionIndicator()}updateConnectionIndicator(){const t=document.querySelector(".connection-status");t&&(t.className="connection-status "+(this.isConnected?"connected":"disconnected"),t.textContent=this.isConnected?"Connected to LAS":"Disconnected")}async checkConnectionStatus(){try{const t=await chrome.runtime.sendMessage({type:"getLASStatus"});this.updateConnectionStatus(t.connected?"connected":"disconnected")}catch(t){console.error("Failed to check connection status:",t),this.updateConnectionStatus("disconnected")}}startRealTimeUpdates(){setInterval((()=>{this.requestStatsUpdate()}),2e3)}async requestStatsUpdate(){try{const t=await chrome.runtime.sendMessage({type:"getSessionStats"});t&&!t.error&&this.updateSessionStats(t)}catch(t){console.error("Failed to get session stats:",t)}}updateEmotionDisplay(t){if(!this.modules.emotion)return;const e=document.getElementById("emotionContent");if(e&&t&&t.emotions){const s=Object.entries(t.emotions).sort((([,t],[,e])=>e-t))[0];e.innerHTML=`\n                <div class="emotion-active">\n                    <div class="emotion-value">${s[0]}</div>\n                    <div class="emotion-confidence">${Math.round(100*s[1])}%</div>\n                    <div class="emotion-timestamp">${(new Date).toLocaleTimeString()}</div>\n                </div>\n            `}}toggleTheme(){document.body.classList.toggle("dark-theme");const t=document.body.classList.contains("dark-theme");chrome.storage.local.set({darkTheme:t})}openSettings(){chrome.tabs.create({url:chrome.runtime.getURL("settings.html")})}openDashboard(){chrome.tabs.create({url:"http://localhost:9876/dashboard"})}}document.addEventListener("DOMContentLoaded",(()=>{new t}))})();