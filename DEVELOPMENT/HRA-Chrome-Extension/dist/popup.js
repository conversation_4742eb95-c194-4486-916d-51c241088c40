(()=>{class e{constructor(){this.isConnected=!1,this.sessionStats={selections:0,copies:0,focusPercentage:100,scrollDepth:0,clicks:0},this.modules={emotion:!1,text:!0,behavioral:!0},this.initialize()}async initialize(){try{await this.loadState(),this.setupEventListeners(),this.updateUI(),this.checkConnectionStatus(),this.startRealTimeUpdates(),console.log("HRA Popup initialized")}catch(e){console.error("Failed to initialize popup:",e)}}async loadState(){return new Promise((e=>{chrome.storage.local.get(["modules","sessionStats","userPreferences","lasConnectionStatus"],(t=>{t.modules&&(this.modules={...this.modules,...t.modules}),t.sessionStats&&(this.sessionStats={...this.sessionStats,...t.sessionStats}),t.lasConnectionStatus&&(this.isConnected="connected"===t.lasConnectionStatus),e()}))}))}setupEventListeners(){const e=document.getElementById("emotionToggle"),t=document.getElementById("textToggle"),s=document.getElementById("behavioralToggle");e&&e.addEventListener("change",(e=>{this.toggleModule("emotion",e.target.checked)})),t&&t.addEventListener("change",(e=>{this.toggleModule("text",e.target.checked)})),s&&s.addEventListener("change",(e=>{this.toggleModule("behavioral",e.target.checked)}));const n=document.getElementById("themeToggle");n&&n.addEventListener("click",(()=>{this.toggleTheme()}));const o=document.getElementById("settingsBtn");o&&o.addEventListener("click",(()=>{this.openSettings()}));const i=document.getElementById("openDashboard");i&&i.addEventListener("click",(e=>{e.preventDefault(),this.openDashboard()}));const a=document.getElementById("openChat");a&&a.addEventListener("click",(e=>{e.preventDefault(),this.openChat()})),chrome.runtime.onMessage.addListener(((e,t,s)=>{console.log("Message received in popup:",e);try{this.handleMessage(e,t,s),s({status:"Message received successfully"})}catch(e){console.error("Error handling message in popup:",e),s({status:"Error handling message",error:e.message})}return!0}))}handleMessage(e,t,s){switch(e.type){case"connectionStatusUpdate":this.updateConnectionStatus(e.status);break;case"sessionStatsUpdate":this.updateSessionStats(e.stats);break;case"emotionDataUpdate":this.updateEmotionDisplay(e.data);break;default:console.log("Unknown message:",e.type)}}async toggleModule(e,t){if(this.modules[e]=t,chrome.storage.local.set({modules:this.modules}),chrome.runtime.sendMessage({type:"toggleModule",module:"emotion"===e?"morphcast":e,enabled:t}),this.updateModuleStatus(e,t),console.log(`Module ${e} ${t?"enabled":"disabled"}`),"emotion"===e)try{const e=await chrome.tabs.query({active:!0,currentWindow:!0});e[0]&&chrome.tabs.sendMessage(e[0].id,{type:"toggleMorphcast",enabled:t})}catch(e){console.error("Failed to toggle MorphCast directly:",e)}}updateModuleStatus(e,t){const s={text:document.getElementById("textStatus"),behavioral:document.getElementById("behavioralStatus")};if(s[e]&&(s[e].textContent=t?"text"===e?"Tracking text selections":"Monitoring engagement":"Disabled"),"emotion"===e){const e=document.getElementById("emotionContent");e&&(e.innerHTML=t?'\n                        <div class="emotion-active">\n                            <div class="emotion-indicator pulse"></div>\n                            <div class="emotion-status">Analyzing emotions...</div>\n                        </div>\n                    ':'\n                        <svg class="icon" width="40" height="40" viewBox="0 0 24 24">\n                            <circle cx="12" cy="8" r="7"></circle>\n                            <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>\n                        </svg>\n                        <div class="emotion-placeholder-text">Enable emotion detection to start capturing your emotional resonance</div>\n                    ')}}updateUI(){const e=document.getElementById("emotionToggle"),t=document.getElementById("textToggle"),s=document.getElementById("behavioralToggle");e&&(e.checked=this.modules.emotion),t&&(t.checked=this.modules.text),s&&(s.checked=this.modules.behavioral),Object.keys(this.modules).forEach((e=>{this.updateModuleStatus(e,this.modules[e])})),this.updateSessionStatsDisplay(),this.updateConnectionIndicator()}updateSessionStatsDisplay(){const e={selectionCount:document.getElementById("selectionCount"),copyCount:document.getElementById("copyCount"),focusPercentage:document.getElementById("focusPercentage"),scrollDepth:document.getElementById("scrollDepth"),clickCount:document.getElementById("clickCount")};e.selectionCount&&(e.selectionCount.textContent=this.sessionStats.selections),e.copyCount&&(e.copyCount.textContent=this.sessionStats.copies),e.focusPercentage&&(e.focusPercentage.textContent=`${this.sessionStats.focusPercentage}%`),e.scrollDepth&&(e.scrollDepth.textContent=`${this.sessionStats.scrollDepth}%`),e.clickCount&&(e.clickCount.textContent=this.sessionStats.clicks)}updateSessionStats(e){this.sessionStats={...this.sessionStats,...e},this.updateSessionStatsDisplay(),chrome.storage.local.set({sessionStats:this.sessionStats})}updateConnectionStatus(e){this.isConnected="connected"===e,this.updateConnectionIndicator()}updateConnectionIndicator(){const e=document.getElementById("statusIndicator"),t=document.getElementById("statusText");e&&t&&(this.isConnected?(e.className="status-indicator status-connected",t.textContent="Connected to Local AI Server"):(e.className="status-indicator status-disconnected",t.textContent="Disconnected from Local AI Server"))}async checkConnectionStatus(){try{if(!chrome.runtime?.id)return console.warn("Extension context invalidated"),void this.updateConnectionStatus("disconnected");const e=await chrome.runtime.sendMessage({type:"getLASStatus"});e&&void 0!==e.connected?this.updateConnectionStatus(e.connected?"connected":"disconnected"):(console.warn("Invalid response from background script:",e),this.updateConnectionStatus("disconnected"))}catch(e){console.error("Failed to check connection status:",e),this.updateConnectionStatus("disconnected")}}startRealTimeUpdates(){setInterval((()=>{this.requestStatsUpdate()}),2e3)}async requestStatsUpdate(){try{const e=await chrome.runtime.sendMessage({type:"getSessionStats"});e&&!e.error&&this.updateSessionStats(e)}catch(e){console.error("Failed to get session stats:",e)}}updateEmotionDisplay(e){if(!this.modules.emotion)return;const t=document.getElementById("emotionContent");if(t&&e&&e.emotions){const s=Object.entries(e.emotions).sort((([,e],[,t])=>t-e))[0];t.innerHTML=`\n                <div class="emotion-active">\n                    <div class="emotion-value">${s[0]}</div>\n                    <div class="emotion-confidence">${Math.round(100*s[1])}%</div>\n                    <div class="emotion-timestamp">${(new Date).toLocaleTimeString()}</div>\n                </div>\n            `}}toggleTheme(){document.body.classList.toggle("dark-theme");const e=document.body.classList.contains("dark-theme");chrome.storage.local.set({darkTheme:e})}openSettings(){chrome.tabs.create({url:chrome.runtime.getURL("settings.html")})}openDashboard(){chrome.tabs.create({url:"http://localhost:9876/dashboard"})}openChat(){chrome.tabs.create({url:"http://localhost:9876/chat"})}}document.addEventListener("DOMContentLoaded",(()=>{new e}))})();