#!/usr/bin/env python3
"""
Simple HRA LLM Integration Example

This script demonstrates how to integrate your LLM with the HRA system
to receive real-time emotion and behavior updates.

Usage:
    python simple_llm_integration.py

Requirements:
    pip install flask requests
"""

from flask import Flask, request, jsonify
import requests
import json
import time
import threading
from datetime import datetime

app = Flask(__name__)

class HRAIntegration:
    def __init__(self):
        self.hra_base_url = "http://localhost:9876"
        self.callback_url = "http://localhost:5000/hra-context"
        self.current_context = {}
        self.emotional_state = "neutral"
        self.interaction_style = "balanced"
        
        print("🎭 HRA LLM Integration Starting...")
        self.check_hra_connection()
        self.subscribe_to_updates()
        
    def check_hra_connection(self):
        """Check if HRA server is running"""
        try:
            response = requests.get(f"{self.hra_base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ HRA server is running")
                return True
            else:
                print(f"❌ HRA server returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Cannot connect to HRA server: {e}")
            print("💡 Make sure HRA server is running on http://localhost:9876")
            return False
    
    def subscribe_to_updates(self):
        """Subscribe to HRA real-time updates"""
        subscription_data = {
            "callbackUrl": self.callback_url,
            "events": ["emotion", "behavior", "summary"]
        }
        
        try:
            response = requests.post(
                f"{self.hra_base_url}/api/llm/subscribe",
                json=subscription_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Subscribed to HRA updates (ID: {result.get('subscriberId')})")
                return True
            else:
                print(f"❌ Failed to subscribe: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Subscription failed: {e}")
            return False
    
    def get_current_context(self):
        """Fetch current user context from HRA"""
        try:
            response = requests.get(f"{self.hra_base_url}/api/stream/context", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"⚠️ Failed to fetch context: {response.status_code}")
                return {}
        except requests.exceptions.RequestException as e:
            print(f"⚠️ Error fetching context: {e}")
            return {}
    
    def process_emotion_update(self, emotion_data):
        """Process incoming emotion updates"""
        current = emotion_data.get('current', {})
        
        if not current:
            return
        
        # Analyze primary emotions
        joy = current.get('joy', 0)
        sadness = current.get('sadness', 0)
        anger = current.get('anger', 0)
        fear = current.get('fear', 0)
        attention = current.get('attention', 0)
        
        # Determine emotional state
        if joy > 0.7:
            self.emotional_state = "positive_mood"
            print("😊 User is feeling happy!")
        elif sadness > 0.6:
            self.emotional_state = "needs_support"
            print("😢 User seems sad, offering support")
        elif anger > 0.6:
            self.emotional_state = "frustrated"
            print("😠 User appears frustrated")
        elif fear > 0.6:
            self.emotional_state = "anxious"
            print("😰 User seems anxious")
        elif attention < 0.3:
            self.emotional_state = "low_attention"
            print("😴 User has low attention")
        else:
            self.emotional_state = "neutral"
        
        # Log emotional metrics
        print(f"📊 Emotion Update: Joy:{joy:.2f} Sadness:{sadness:.2f} Attention:{attention:.2f}")
    
    def process_behavior_update(self, behavior_data):
        """Process incoming behavior updates"""
        engagement = behavior_data.get('engagement', 0)
        activity = behavior_data.get('activity', {})
        patterns = behavior_data.get('patterns', {})
        
        # Determine interaction style based on engagement
        if engagement > 80:
            self.interaction_style = "detailed_responses"
            print("🔥 High engagement - using detailed responses")
        elif engagement < 30:
            self.interaction_style = "brief_engaging"
            print("💤 Low engagement - using brief, engaging responses")
        else:
            self.interaction_style = "balanced"
            print("⚖️ Moderate engagement - using balanced responses")
        
        # Log behavioral metrics
        activity_level = activity.get('intensity', 'unknown')
        print(f"📈 Behavior Update: Engagement:{engagement}% Activity:{activity_level}")
        
        # Analyze patterns
        for pattern_type, pattern_data in patterns.items():
            frequency = pattern_data.get('frequency', 0)
            trend = pattern_data.get('trend', 'stable')
            print(f"🔍 Pattern: {pattern_type} - Frequency:{frequency} Trend:{trend}")
    
    def process_context_summary(self, summary_data):
        """Process context summary updates"""
        session_id = summary_data.get('session', 'unknown')
        emotional_state = summary_data.get('emotionalState', {})
        behavior_summary = summary_data.get('behaviorSummary', {})
        session_duration = summary_data.get('sessionDuration', 0)
        
        duration_minutes = session_duration / 60000  # Convert to minutes
        
        print(f"📋 Context Summary:")
        print(f"   Session: {session_id[:8]}...")
        print(f"   Duration: {duration_minutes:.1f} minutes")
        print(f"   Emotional State: {self.emotional_state}")
        print(f"   Interaction Style: {self.interaction_style}")
    
    def generate_contextual_response(self, user_message):
        """Generate a response considering current context"""
        context = self.get_current_context()
        
        # Build context-aware prompt
        prompt = f"""
        User Message: {user_message}
        
        Current Context:
        - Emotional State: {self.emotional_state}
        - Interaction Style: {self.interaction_style}
        - Session Stats: {context.get('sessionStats', {})}
        
        Generate an appropriate response considering the user's current emotional and behavioral state.
        """
        
        # Simulate LLM response (replace with your actual LLM call)
        response = self.simulate_llm_response(user_message)
        
        # Adapt response based on emotional state
        adapted_response = self.adapt_response_to_emotion(response)
        
        return adapted_response
    
    def simulate_llm_response(self, user_message):
        """Simulate LLM response (replace with your actual LLM integration)"""
        responses = {
            "hello": "Hello! How can I help you today?",
            "how are you": "I'm doing well, thank you for asking!",
            "help": "I'm here to assist you. What do you need help with?",
            "default": "I understand. Let me help you with that."
        }
        
        message_lower = user_message.lower()
        for key, response in responses.items():
            if key in message_lower:
                return response
        
        return responses["default"]
    
    def adapt_response_to_emotion(self, base_response):
        """Adapt response based on current emotional state"""
        if self.emotional_state == "positive_mood":
            return f"That's wonderful! {base_response} I'm excited to help you with this! 🎉"
        elif self.emotional_state == "needs_support":
            return f"I understand this might be challenging. {base_response} I'm here to support you. 🤗"
        elif self.emotional_state == "low_attention":
            return f"Quick answer: {base_response[:50]}... Would you like me to elaborate? 💡"
        elif self.emotional_state == "frustrated":
            return f"I can see this is frustrating. Let's work through this step by step. {base_response} 🧘"
        elif self.emotional_state == "anxious":
            return f"Take your time, there's no rush. {base_response} Everything will be okay. 🌟"
        else:
            return base_response

# Global HRA integration instance
hra = HRAIntegration()

@app.route('/hra-context', methods=['POST'])
def receive_hra_update():
    """Endpoint to receive HRA context updates"""
    try:
        update_data = request.json
        category = update_data.get('category', 'unknown')
        data = update_data.get('data', {})
        timestamp = update_data.get('timestamp', datetime.now().isoformat())
        
        print(f"\n🔄 [{datetime.now().strftime('%H:%M:%S')}] HRA Update: {category}")
        
        # Process different types of updates
        if category == 'emotion':
            hra.process_emotion_update(data)
        elif category == 'behavior':
            hra.process_behavior_update(data)
        elif category == 'summary':
            hra.process_context_summary(data)
        else:
            print(f"📦 Unknown update type: {category}")
        
        # Store current context
        hra.current_context.update(update_data)
        
        return jsonify({"status": "received", "timestamp": timestamp})
        
    except Exception as e:
        print(f"❌ Error processing HRA update: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/chat', methods=['POST'])
def chat_endpoint():
    """Chat endpoint with HRA context integration"""
    try:
        data = request.json
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({"error": "Message is required"}), 400
        
        print(f"\n💬 User: {user_message}")
        
        # Generate context-aware response
        response = hra.generate_contextual_response(user_message)
        
        print(f"🤖 Bot: {response}")
        
        return jsonify({
            "response": response,
            "emotional_state": hra.emotional_state,
            "interaction_style": hra.interaction_style,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/status', methods=['GET'])
def status_endpoint():
    """Get current integration status"""
    context = hra.get_current_context()
    
    return jsonify({
        "status": "running",
        "emotional_state": hra.emotional_state,
        "interaction_style": hra.interaction_style,
        "hra_connected": hra.check_hra_connection(),
        "current_context": context,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/', methods=['GET'])
def home():
    """Simple home page"""
    return """
    <html>
    <head><title>HRA LLM Integration</title></head>
    <body>
        <h1>🎭 HRA LLM Integration</h1>
        <p>Your LLM is connected to HRA and receiving real-time context updates!</p>
        
        <h2>📊 Current Status</h2>
        <p>Emotional State: <span id="emotion">Loading...</span></p>
        <p>Interaction Style: <span id="style">Loading...</span></p>
        
        <h2>💬 Test Chat</h2>
        <input type="text" id="message" placeholder="Type a message..." style="width: 300px;">
        <button onclick="sendMessage()">Send</button>
        <div id="response" style="margin-top: 10px; padding: 10px; background: #f0f0f0;"></div>
        
        <script>
            function updateStatus() {
                fetch('/status')
                    .then(r => r.json())
                    .then(data => {
                        document.getElementById('emotion').textContent = data.emotional_state;
                        document.getElementById('style').textContent = data.interaction_style;
                    });
            }
            
            function sendMessage() {
                const message = document.getElementById('message').value;
                if (!message) return;
                
                fetch('/chat', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({message: message})
                })
                .then(r => r.json())
                .then(data => {
                    document.getElementById('response').innerHTML = 
                        '<strong>You:</strong> ' + message + '<br>' +
                        '<strong>Bot:</strong> ' + data.response;
                    document.getElementById('message').value = '';
                });
            }
            
            // Update status every 5 seconds
            setInterval(updateStatus, 5000);
            updateStatus();
            
            // Allow Enter key to send message
            document.getElementById('message').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') sendMessage();
            });
        </script>
    </body>
    </html>
    """

def periodic_context_check():
    """Periodically check and log context (for demonstration)"""
    while True:
        time.sleep(30)  # Check every 30 seconds
        try:
            context = hra.get_current_context()
            session_stats = context.get('sessionStats', {})
            
            if session_stats:
                print(f"\n📊 Periodic Check:")
                print(f"   Active Sessions: {session_stats.get('activeSessions', 0)}")
                print(f"   Behavioral Events: {session_stats.get('behavioralEvents', 0)}")
                print(f"   Emotions Captured: {session_stats.get('emotions', 0)}")
                print(f"   Current State: {hra.emotional_state}")
        except Exception as e:
            print(f"⚠️ Periodic check failed: {e}")

if __name__ == '__main__':
    print("\n🎭 HRA LLM Integration Server")
    print("=" * 50)
    print("📡 Webhook endpoint: http://localhost:5000/hra-context")
    print("💬 Chat endpoint: http://localhost:5000/chat")
    print("📊 Status endpoint: http://localhost:5000/status")
    print("🌐 Web interface: http://localhost:5000")
    print("=" * 50)
    
    # Start periodic context checking in background
    context_thread = threading.Thread(target=periodic_context_check, daemon=True)
    context_thread.start()
    
    # Start Flask server
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 Shutting down HRA LLM Integration...")
