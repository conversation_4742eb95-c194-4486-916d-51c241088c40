<!DOCTYPE html>
<html>
<head>
    <title>HRA Camera Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        video { width: 400px; height: 300px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; }
        #status { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>HRA Camera Permission Test</h1>
    <div id="status">Status: Not started</div>
    <button onclick="testCamera()">Test Camera</button>
    <button onclick="testMorphCast()">Test MorphCast</button>
    <br>
    <video id="video" autoplay muted></video>

    <script>
        async function testCamera() {
            const status = document.getElementById('status');
            const video = document.getElementById('video');
            
            try {
                status.innerHTML = 'Status: Requesting camera permission...';
                
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        facingMode: 'user'
                    } 
                });
                
                video.srcObject = stream;
                status.innerHTML = 'Status: ✅ Camera working! Stream active.';
                
                console.log('Camera stream:', stream);
                console.log('Video tracks:', stream.getVideoTracks());
                
            } catch (error) {
                status.innerHTML = `Status: ❌ Camera failed: ${error.message}`;
                console.error('Camera error:', error);
            }
        }
        
        async function testMorphCast() {
            const status = document.getElementById('status');
            
            try {
                status.innerHTML = 'Status: Loading MorphCast SDK...';
                
                // Load MorphCast SDK
                const script = document.createElement('script');
                script.src = 'https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js';
                script.async = true;
                script.crossOrigin = 'anonymous';
                
                script.onload = () => {
                    status.innerHTML = 'Status: ✅ MorphCast SDK loaded!';
                    console.log('MorphCast available:', window.MorphCast);
                    
                    if (window.MorphCast) {
                        status.innerHTML = 'Status: ✅ MorphCast SDK ready for initialization';
                    }
                };
                
                script.onerror = () => {
                    status.innerHTML = 'Status: ❌ Failed to load MorphCast SDK';
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                status.innerHTML = `Status: ❌ MorphCast test failed: ${error.message}`;
                console.error('MorphCast error:', error);
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', () => {
            console.log('Test page loaded');
        });
    </script>
</body>
</html>