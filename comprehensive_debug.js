// HRA Extension Comprehensive Debug Script
// Run this in browser console to diagnose issues

window.HRADebug = {
    async runFullDiagnostic() {
        console.clear();
        console.log('🚀 HRA Extension Full Diagnostic Started');
        console.log('=====================================\n');
        
        // Test 1: Extension Context
        await this.testExtensionContext();
        
        // Test 2: Camera Access
        await this.testCameraAccess();
        
        // Test 3: MorphCast SDK
        await this.testMorphCastSDK();
        
        // Test 4: WebSocket Connection
        await this.testWebSocketConnection();
        
        // Test 5: Content Script Communication
        await this.testContentScriptCommunication();
        
        console.log('\n🏁 Full Diagnostic Complete');
        console.log('=====================================');
    },
    
    async testExtensionContext() {
        console.log('1️⃣ Testing Extension Context...');
        
        try {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                console.log('   ✅ Chrome Extension context available');
                console.log('   📋 Extension ID:', chrome.runtime.id);
                
                // Test background connection
                try {
                    const response = await chrome.runtime.sendMessage({ type: 'debug_ping' });
                    console.log('   ✅ Background script responding:', response);
                } catch (error) {
                    console.log('   ⚠️ Background script error:', error.message);
                }
            } else {
                console.log('   ❌ Chrome Extension context not available');
            }
        } catch (error) {
            console.log('   ❌ Extension context error:', error.message);
        }
        
        console.log('');
    },
    
    async testCameraAccess() {
        console.log('2️⃣ Testing Camera Access...');
        
        try {
            // Check if getUserMedia is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.log('   ❌ getUserMedia not available');
                return;
            }
            
            console.log('   📹 Requesting camera permission...');
            
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                } 
            });
            
            console.log('   ✅ Camera access granted!');
            console.log('   📊 Video tracks:', stream.getVideoTracks().length);
            
            // Test video track settings
            const videoTrack = stream.getVideoTracks()[0];
            if (videoTrack) {
                const settings = videoTrack.getSettings();
                console.log('   📐 Video settings:', {
                    width: settings.width,
                    height: settings.height,
                    frameRate: settings.frameRate
                });
            }
            
            // Clean up
            stream.getTracks().forEach(track => track.stop());
            console.log('   🛑 Camera stream stopped');
            
        } catch (error) {
            console.log('   ❌ Camera access failed:', error.message);
            console.log('   💡 Error name:', error.name);
            
            if (error.name === 'NotAllowedError') {
                console.log('   💡 Permission denied by user or browser policy');
            } else if (error.name === 'NotFoundError') {
                console.log('   💡 No camera device found');
            }
        }
        
        console.log('');
    },
    
    async testMorphCastSDK() {
        console.log('3️⃣ Testing MorphCast SDK...');
        
        try {
            // Check if already loaded
            if (window.MorphCast) {
                console.log('   ✅ MorphCast SDK already loaded');
                console.log('   📋 Available modules:', Object.keys(window.MorphCast.Modules || {}));
                return;
            }
            
            console.log('   📦 Loading MorphCast SDK...');
            
            await new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://sdk.morphcast.com/mcsdk/v2/mcsdk.min.js';
                script.async = true;
                script.crossOrigin = 'anonymous';
                
                script.onload = () => {
                    setTimeout(() => {
                        if (window.MorphCast) {
                            console.log('   ✅ MorphCast SDK loaded successfully');
                            console.log('   📋 Available modules:', Object.keys(window.MorphCast.Modules || {}));
                            resolve();
                        } else {
                            console.log('   ❌ MorphCast SDK loaded but not available');
                            reject(new Error('SDK not available'));
                        }
                    }, 100);
                };
                
                script.onerror = () => {
                    console.log('   ❌ Failed to load MorphCast SDK');
                    reject(new Error('Script load failed'));
                };
                
                document.head.appendChild(script);
            });
            
            // Test initialization
            try {
                const testConfig = {
                    license: 'sk08f1205ae26b35a77829772fe21d8edd83022a21ef7b',
                    email: '<EMAIL>',
                    modules: [window.MorphCast.Modules.FACE_EMOTION]
                };
                
                console.log('   🧪 Testing MorphCast initialization...');
                // Note: Don't actually initialize to avoid license usage
                console.log('   ✅ MorphCast ready for initialization');
                
            } catch (initError) {
                console.log('   ⚠️ MorphCast initialization test failed:', initError.message);
            }
            
        } catch (error) {
            console.log('   ❌ MorphCast SDK test failed:', error.message);
        }
        
        console.log('');
    },
    
    async testWebSocketConnection() {
        console.log('4️⃣ Testing WebSocket Connection...');
        
        try {
            console.log('   🔌 Attempting connection to ws://localhost:9876...');
            
            const ws = new WebSocket('ws://localhost:9876');
            
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    ws.close();
                    reject(new Error('Connection timeout'));
                }, 5000);
                
                ws.onopen = () => {
                    clearTimeout(timeout);
                    console.log('   ✅ WebSocket connection successful');
                    
                    // Test message sending
                    ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
                    console.log('   📤 Test message sent');
                    
                    resolve();
                };
                
                ws.onmessage = (event) => {
                    console.log('   📥 Message received:', event.data);
                };
                
                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    console.log('   ❌ WebSocket error:', error);
                    reject(error);
                };
                
                ws.onclose = (event) => {
                    console.log('   🔌 WebSocket closed:', event.code, event.reason);
                };
            });
            
            ws.close();
            
        } catch (error) {
            console.log('   ❌ WebSocket connection failed:', error.message);
            console.log('   💡 Make sure Local AI Server is running on port 9876');
        }
        
        console.log('');
    },
    
    async testContentScriptCommunication() {
        console.log('5️⃣ Testing Content Script Communication...');
        
        try {
            // Test window messaging (for inject.js communication)
            let messageReceived = false;
            
            const messageHandler = (event) => {
                if (event.source === window && event.data && event.data.type === 'debug_response') {
                    messageReceived = true;
                    console.log('   ✅ Window message communication working');
                    window.removeEventListener('message', messageHandler);
                }
            };
            
            window.addEventListener('message', messageHandler);
            
            // Send test message
            window.postMessage({ type: 'debug_test', timestamp: Date.now() }, '*');
            
            // Wait for response
            await new Promise(resolve => {
                setTimeout(() => {
                    if (!messageReceived) {
                        console.log('   ⚠️ No response to window message (inject.js may not be loaded)');
                        window.removeEventListener('message', messageHandler);
                    }
                    resolve();
                }, 1000);
            });
            
        } catch (error) {
            console.log('   ❌ Content script communication test failed:', error.message);
        }
        
        console.log('');
    }
};

// Auto-run diagnostic
console.log('🔧 HRA Debug Tools Loaded');
console.log('Run HRADebug.runFullDiagnostic() to start comprehensive testing');

// Run automatically if requested
if (window.location.search.includes('auto-debug')) {
    setTimeout(() => {
        window.HRADebug.runFullDiagnostic();
    }, 1000);
}
