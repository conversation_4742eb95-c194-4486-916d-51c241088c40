{"name": "hra-chrome-extension", "version": "1.0.0", "description": "Harmonic Resonance Agent - Client-side AI companion with persistent emotional memory", "main": "background.js", "scripts": {"build": "webpack --mode=production", "dev": "webpack --mode=development --watch", "lint": "eslint src/**/*.js", "format": "prettier --write src/**/*.{js,html,css}", "test": "echo \"Error: no test specified\" && exit 1", "web-ext:build": "web-ext build --source-dir=dist", "web-ext:run": "web-ext run --source-dir=dist --target=chromium"}, "keywords": ["chrome-extension", "ai", "emotion-detection", "behavioral-tracking", "morphcast"], "author": "<PERSON> <gabe<PERSON><PERSON><PERSON><PERSON>@mac.com>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/hra-chrome-extension.git"}, "devDependencies": {"clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "eslint": "^9.28.0", "html-webpack-plugin": "^5.6.3", "prettier": "^3.5.3", "sharp": "^0.34.2", "style-loader": "^4.0.0", "web-ext": "^8.7.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}